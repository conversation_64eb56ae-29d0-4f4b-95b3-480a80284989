package com.altomni.apn.common.vo.recruiting;

import cn.hutool.core.util.ReUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import com.altomni.apn.common.utils.CommonUtils;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.Instant;
import java.util.Arrays;
import java.util.List;

import static com.altomni.apn.common.utils.CommonUtils.CHINESE_CHARACTERS;

@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class RecruitingKpiCommonCountVO {

    //by user
    private Long userId;

    private String userName;

    private Long teamId;

    private String teamName;

    private Long tenantId;

    //by company
    private Long companyId;

    private String companyName;

    private Long jobId;

    private String jobTitle;

    //common
    private String groupByDate;

    private Long countNum;

    private Long companyNum;

    private String ids;

    private String industries;

    private String country;

    @JsonIgnore
    private String key;

    public String getUserName() {
        if (StrUtil.isBlank(userName)) {
            return null;
        }
        if (((ReUtil.count(CHINESE_CHARACTERS, userName)) > 0)) {
            String[] names = userName.split(" ");
            if (names.length > 1) {
                return CommonUtils.formatFullName(names[0], names[1]);
            }
        }
        return userName;
    }
}
