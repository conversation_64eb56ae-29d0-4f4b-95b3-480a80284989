package com.altomni.apn.user.web.rest.vm.permission;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Set;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class PermissionRoleTeamVM {

    private PermissionDetail jobPermission;

    private PermissionDetail clientContactPermission;

    private PermissionDetail reportPermission;

    private PermissionDetail homeAndCalendarPermission;

    private PermissionDetail candidatePipelineManagementPermission;

    private PermissionDetail chinaInvoicingModulePermission;

    private PermissionDetail confidentialTalentPermission;

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @JsonInclude(JsonInclude.Include.NON_NULL)
    public static class PermissionDetail{

        private Long roleId;

        private Integer dataScope;

        private boolean modifiable = false;

        private Set<Long> teamIds;
    }

}
