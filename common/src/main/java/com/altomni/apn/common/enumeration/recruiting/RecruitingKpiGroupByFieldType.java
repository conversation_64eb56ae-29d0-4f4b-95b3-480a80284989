package com.altomni.apn.common.enumeration.recruiting;

import java.util.List;

public enum RecruitingKpiGroupByFieldType {

    /**
     * RECRUITING KPI BY COMPANY : Company, Job, Day, Week, Month, Quarter, Year
     */
    COMPANY, JOB,

    /**
     * RECRUITING KPI BY USER : USER, TEAM, Day, Week, Month, Quarter, Year
     */
    USER, TEAM, TENANT,

    /**
     * RECRUITING KPI BY USER/COMPANY : Day, Week, Month, Quarter, Year
     */
    DAY, WEEK, MONTH, QUARTER, YEAR;

    public static final List<RecruitingKpiGroupByFieldType> ALL_TIME_LIST = List.of(DAY, WEEK, MONTH, QUARTER, YEAR);

}
