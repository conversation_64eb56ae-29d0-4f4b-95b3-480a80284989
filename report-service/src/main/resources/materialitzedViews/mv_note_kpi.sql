-- note 相关的物化视图，包含 talent_note,application_note,apn_pro_tracking_note

CREATE
MATERIALIZED VIEW IF NOT EXISTS ods_apn.mv_note_kpi
       DISTRIBUTED BY HASH (`user_id`)
REFRESH
       ASYNC EVERY (INTERVAL 10 MINUTE)
ORDER BY (add_date)
AS
SELECT *
FROM ((
-- talent_note
          SELECT u.tenant_id                            AS                 tenant_id,
                 pt.id                                  AS                 team_id,
                 pt.name                                AS                 team_name,
                 u.id                                   AS                 user_id,
                 CONCAT(u.first_name, ' ', u.last_name) AS                 user_name,
                 u.activated                            AS                 user_activated,
                 tn.created_date                        AS                 add_date,
                 BITMAP_AGG(CASE WHEN tn.note_type = 0 THEN tn.id END)     callNoteNum,
                 BITMAP_AGG(CASE WHEN tn.note_type = 1 THEN tn.id END)     personNoteNum,
                 BITMAP_AGG(CASE WHEN tn.note_type = 2 THEN tn.id END)     otherNoteNum,
                 BITMAP_AGG(CASE WHEN tn.note_type = 3 THEN tn.id END)     emailNoteNum,
                 BITMAP_AGG(CASE WHEN tn.note_type = 4 THEN tn.id END)     videoNoteNum,
                 BITMAP_AGG(CASE WHEN tn.note_type = 5 THEN tn.id END)     iciNum,
                 BITMAP_AGG(CASE WHEN tn.agency_id IS NULL THEN tn.id END) noteCount,
                 BITMAP_AGG(tn.talent_id)               as                 unique_talent_ids,
                 BITMAP_EMPTY()                         AS                 application_note_count_num,
                 BITMAP_EMPTY()                         AS                 talent_tracking_note_count_num,
                 BITMAP_EMPTY()                         AS                 talent_tracking_note_ids
          FROM talent_note tn
                   inner join user u on u.id = tn.puser_id
                   inner join permission_user_team put on put.user_id = tn.puser_id and put.is_primary = 1
                   inner join permission_team pt on pt.id = put.team_id
          GROUP BY u.tenant_id, pt.id, pt.name, u.id, u.first_name, u.last_name, u.activated, tn.created_date)
      UNION ALL
      -- application note
      (SELECT u.tenant_id                            AS tenant_id,
              pt.id                                  AS team_id,
              pt.name                                AS team_name,
              u.id                                   AS user_id,
              CONCAT(u.first_name, ' ', u.last_name) AS user_name,
              u.activated                            AS user_activated,
              node.created_date                      AS add_date,
              BITMAP_EMPTY()                         AS callNoteNum,
              BITMAP_EMPTY()                         AS personNoteNum,
              BITMAP_EMPTY()                         AS otherNoteNum,
              BITMAP_EMPTY()                         AS emailNoteNum,
              BITMAP_EMPTY()                         AS videoNoteNum,
              BITMAP_EMPTY()                         AS iciNum,
              BITMAP_EMPTY()                         AS noteCount,
              BITMAP_EMPTY()                         as unique_talent_ids,
              BITMAP_AGG(node.node_id)               AS application_note_count_num,
              BITMAP_EMPTY()                         AS talent_tracking_note_count_num,
              BITMAP_EMPTY()                         AS talent_tracking_note_ids
       FROM talent_recruitment_process_note node
                INNER JOIN talent_recruitment_process trp ON trp.id = node.talent_recruitment_process_id
                inner join talent_recruitment_process_kpi_user ul on ul.talent_recruitment_process_id = trp.id
                inner join job j on j.id = trp.job_id
                inner join talent t on t.id = trp.talent_id
                inner JOIN talent_recruitment_process_node trpn
                           ON trp.id = trpn.talent_recruitment_process_id
                INNER JOIN permission_user_team put ON put.user_id = node.user_id and put.is_primary = 1
                inner join permission_team pt on pt.id = put.team_id
                inner join user u on u.id = put.user_id
       GROUP BY u.tenant_id, pt.id, pt.name, u.id, u.first_name, u.last_name, u.activated, node.created_date)
      UNION ALL
-- apn pro tracking_note
      (SELECT ttn.tenant_id                          AS tenant_id,
              pt.id                                  AS team_id,
              pt.name                                AS team_name,
              u.id                                   AS user_id,
              CONCAT(u.first_name, ' ', u.last_name) AS user_name,
              u.activated                            AS user_activated,
              ttn.created_date                       AS add_date,
              BITMAP_EMPTY()                         AS callNoteNum,
              BITMAP_EMPTY()                         AS personNoteNum,
              BITMAP_EMPTY()                         AS otherNoteNum,
              BITMAP_EMPTY()                         AS emailNoteNum,
              BITMAP_EMPTY()                         AS videoNoteNum,
              BITMAP_EMPTY()                         AS iciNum,
              BITMAP_EMPTY()                         AS noteCount,
              BITMAP_EMPTY()                         as unique_talent_ids,
              BITMAP_EMPTY()                         AS application_note_count_num,
              BITMAP_AGG(ttn.id)                     AS talent_tracking_note_count_num,
              BITMAP_AGG(ttn.id)                     AS talent_tracking_note_ids
       FROM talent_tracking_note ttn
                INNER JOIN talent t ON t.id = ttn.synced_talent_id
                inner join user u on u.id = ttn.user_id
                inner join permission_user_team put on put.user_id = ttn.user_id and put.is_primary = 1
                inner join permission_team pt on pt.id = put.team_id
       GROUP BY ttn.tenant_id, pt.id, pt.name, u.id, u.first_name, u.last_name, u.activated,
                ttn.created_date)) AS final_table;