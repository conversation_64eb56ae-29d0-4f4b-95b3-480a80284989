package com.altomni.apn.report.config;

import org.jetbrains.annotations.NotNull;

import java.util.concurrent.Executor;

public class StarRocksDelegatingExecutor implements Executor {
    private final Executor delegate;

    public StarRocksDelegatingExecutor(Executor delegate) {
        this.delegate = delegate;
    }

    @Override
    public void execute(@NotNull Runnable runnable) {
        this.delegate.execute(new StarRocksDelegatingRunnable(runnable));
    }
}
