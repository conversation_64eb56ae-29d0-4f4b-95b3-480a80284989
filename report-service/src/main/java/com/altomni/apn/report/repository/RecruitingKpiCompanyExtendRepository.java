package com.altomni.apn.report.repository;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.altomni.apn.common.domain.enumeration.job.JobType;
import com.altomni.apn.common.dto.permission.TeamDataPermissionRespDTO;
import com.altomni.apn.common.enumeration.recruiting.RecruitingKpiApplicationStatusType;
import com.altomni.apn.common.enumeration.recruiting.RecruitingKpiDateType;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.finance.service.vo.invoice.UserCountryVO;
import com.altomni.apn.report.domain.enumeration.ReportApplicationStatus;
import com.altomni.apn.report.domain.vo.*;
import com.altomni.apn.report.dto.*;
import com.altomni.apn.report.util.MapToEntityUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.hibernate.SQLQuery;
import org.hibernate.transform.Transformers;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Repository;
import org.springframework.util.StopWatch;

import javax.persistence.Query;
import java.util.*;

import static com.altomni.apn.report.domain.enumeration.ReportApplicationStatus.INTERVIEW_APPOINTMENTS;

@Slf4j
@Repository
public class RecruitingKpiCompanyExtendRepository extends RecruitingKpiBaseRepository {

    public List<RecruitingKpiJobDetailVO> searchJobDetailList(RecruitingKpiJobDetailSearchDto jobSearchDto, Pageable pageable) {
        StopWatch stopWatch = new StopWatch("searchJobDetailList");
        stopWatch.start();
        String sql = """
                SELECT
                	j.id job_id,
                	j.posting_time posting_date,
                	j.open_time open_date,
                	website.last_modified_date tenant_website_posting_date,
                	j.title job_title,
	                j.pteam_id,
                	j.code job_code,
                	j.status job_status,
                	j.openings,
                	rp.id recruitment_process_id,
                	rp.name job_type_name,
                	rp.job_type job_type,
                	c.`full_business_name` company_name,
                	c.id company_id,
                	jai.extended_info ->> '$.department' AS division,
                	GROUP_CONCAT( DISTINCT concat(tcslcc.id,"-",tcslcc.full_name) ) client_contact,
                	GROUP_CONCAT( DISTINCT concat(u.id, "-", u.first_name, " ", u.last_name)) assigned_user,
                	CAST(jai.extended_info ->> '$.salaryRange.gte' AS DECIMAL) AS minimum_pay_rate,
                    CAST(jai.extended_info ->> '$.salaryRange.lte' AS DECIMAL) AS maximum_pay_rate,
                    CAST(jai.extended_info ->> '$.billRange.gte' AS DECIMAL) AS minimum_bill_rate,
                    CAST(jai.extended_info ->> '$.billRange.lte' AS DECIMAL) AS maximum_bill_rate,
                	jai.extended_info ->> '$.payType' AS rate_per,
                	j.start_date,
                	j.end_date,
                	j.contract_duration,
                	IFNULL(jar.submit_to_client, 0) submit_to_client_num,
                    IFNULL(jar.first_interview, 0) first_interview_num,
                    IFNULL(jar.second_interview, 0) second_interview_num,
                    IFNULL(jar.final_interview, 0) final_interview_num,
                    IFNULL(jar.interview, 0) interview_num,
                    IFNULL(jar.offer, 0) offer_num,
                    IFNULL(jar.onboard, 0) onboard_num,
                	GROUP_CONCAT(distinct jl.format_location SEPARATOR ";") job_location,
                	j.flexible_location,
                	jai.extended_info ->> '$.requiredSkills[*].skillName' AS skills,
                	ec.id currency,
                	ec.name ec_name,
                	ec.symbol job_currency,
                	if(j.sales_lead_id is null, null, if(business_progress = 60, "Master Contract", "Trial Case")) job_cooperation_status,
                	count( DISTINCT jn.id ) contract_notes
                FROM
                	job j
                	INNER JOIN company c ON c.id = j.company_id
	                INNER JOIN company_user_relation ul ON ul.company_id = c.id
                	Inner join permission_user_team put on put.user_id = ul.user_id and put.is_primary = 1
                	INNER join permission_team pt on pt.id = put.team_id
                	INNER JOIN recruitment_process rp ON rp.id = j.recruitment_process_id
                	left join company_industry_relation cir on cir.company_id = c.id
                	LEFT JOIN job_company_contact_relation jccr ON jccr.job_id = j.id
                	LEFT JOIN company_sales_lead_client_contact cslcc ON cslcc.id = jccr.client_contact_id
                	LEFT JOIN talent tcslcc ON tcslcc.id = cslcc.talent_id
                	LEFT JOIN user_job_relation ujr ON ujr.job_id = j.id AND ujr.status = 1
                	LEFT JOIN user u on u.id = ujr.user_id
                	LEFT JOIN job_additional_info jai ON jai.id = j.additional_info_id
                	LEFT JOIN job_location jl ON jl.job_id = j.id
                	LEFT JOIN job_note jn ON jn.job_id = j.id and jn.visible = 1
                	left join job_application_relation jar on jar.job_id = j.id
                	left join enum_currency ec on ec.id = j.currency
                	left join account_business ab on ab.id = j.sales_lead_id
                	left join job_ipg_relation website on website.apn_job_id = j.id and website.ipg_job_status not in (4, 200) 
                	where 1=1 
                	{whereCondition}
                	group by j.id
                	{orderBy}
                """;
        Map<String, Object> whereParamMap = new HashMap<>(16);
        Map<String, String> map = new HashMap<>(16);
        map.put("whereCondition", getJobDetailWhereConditionSqlByCompany(jobSearchDto, whereParamMap));
        map.put("orderBy", jobSearchDto.getOrderBySql());
        sql = StrUtil.format(sql, map);
        Query query = entityManager.createNativeQuery(sql, RecruitingKpiJobDetailVO.class);
        setListWithPage(pageable, whereParamMap, query);
        List<RecruitingKpiJobDetailVO> voList = query.getResultList();
        stopWatch.stop();
        log.info(" searchJobDetailList time = {} ms= \n {}", stopWatch.getTotalTimeMillis(), stopWatch.prettyPrint());
        return voList;
    }

    public Long searchJobDetailCount(RecruitingKpiJobDetailSearchDto jobSearchDto) {
        StopWatch stopWatch = new StopWatch("searchJobDetailCount");
        stopWatch.start();
        String sql = """
                SELECT
                	count(distinct j.id)
                FROM
                	job j
                	INNER JOIN company c ON c.id = j.company_id
	                INNER JOIN company_user_relation ul ON ul.company_id = c.id
                	Inner join permission_user_team put on put.user_id = ul.user_id and put.is_primary = 1
                    INNER join permission_team pt on pt.id = put.team_id
                	INNER JOIN recruitment_process rp ON rp.id = j.recruitment_process_id
                	LEFT JOIN job_company_contact_relation jccr ON jccr.job_id = j.id
                	LEFT JOIN company_sales_lead_client_contact cslcc ON cslcc.id = jccr.client_contact_id
                	LEFT JOIN talent tcslcc ON tcslcc.id = cslcc.talent_id
                	LEFT JOIN user_job_relation ujr ON ujr.job_id = j.id AND ujr.status = 1
                	LEFT JOIN job_additional_info jai ON jai.id = j.additional_info_id
                	LEFT JOIN job_location jl ON jl.job_id = j.id
                	where 1=1
                	{whereCondition}
                """;
        Map<String, String> map = new HashMap<>(16);
        Map<String, Object> whereParamMap = new HashMap<>(16);
        map.put("whereCondition", getJobDetailWhereConditionSqlByCompany(jobSearchDto, whereParamMap));
        sql = StrUtil.format(sql, map);
        Query countQ = entityManager.createNativeQuery(sql);
        Optional.of(whereParamMap).ifPresent(m -> m.forEach(countQ::setParameter));
        long count = Long.parseLong(String.valueOf(countQ.getSingleResult()));
        stopWatch.stop();
        log.info(" searchJobDetailCount time = {} ms= \n {}", stopWatch.getTotalTimeMillis(), stopWatch.prettyPrint());
        return count;
    }

    public List<RecruitingKpiTalentDetailVO> searchTalentDetailList(RecruitingKpiTalentDetailSearchDto searchDto, Pageable pageable) {
        StopWatch stopWatch = new StopWatch("searchTalentDetailList");
        stopWatch.start();
        String sql;
        Map<String, Object> whereParamMap = new HashMap<>(16);
        Map<String, String> map = new HashMap<>(16);
        sql = """
            SELECT
                t.id AS talent_id,
                t.full_name AS full_name,
                tai.current_company,
                tai.current_position,
                t.motivation_id job_search_status,
                t.puser_id created_by,
                t.created_date,
                t.last_update_user_id last_modified_by,
                t.last_modified_date,
                GROUP_CONCAT(DISTINCT CASE WHEN tn.note_type = 5 THEN 'ICI' END) AS note_type,
                count( DISTINCT tn.id ) talent_notes,
                IF(cslcc.id IS NOT NULL AND cslcc.STATUS = 1, TRUE, FALSE ) is_contact
            FROM
                talent t
                FORCE INDEX (idx_talent_tenant_id_created_date)
                INNER JOIN talent_recruitment_process trp ON trp.talent_id = t.id
                INNER JOIN job j on j.id = trp.job_id
                INNER JOIN company c on c.id = j.company_id
                INNER JOIN company_user_relation ul ON ul.company_id = c.id
                INNER JOIN permission_user_team put ON put.user_id = ul.user_id and put.is_primary = 1
                INNER join permission_team pt on pt.id = put.team_id
                left JOIN recruitment_process rp ON rp.id = j.recruitment_process_id
                left join company_industry_relation cir on cir.company_id = c.id
                LEFT JOIN talent_note tn ON tn.talent_id = t.id
                LEFT JOIN talent_additional_info tai ON tai.id = t.additional_info_id
                left join company_sales_lead_client_contact cslcc on cslcc.talent_id = t.id
            WHERE
            t.tenant_id = :tenantId {dateSearchSql}
            {whereCondition}
            group by t.id
            {orderBy}
            """;
        map.put("dateSearchSql", " and t.created_date BETWEEN :startDate AND :endDate ");
        whereParamMap.put("tenantId", searchDto.getSearchTenantId());
        whereParamMap.put("startDate", searchDto.getStartDateUtc());
        whereParamMap.put("endDate", searchDto.getEndDateUtc());
        map.put("orderBy", searchDto.getOrderBySql());
        map.put("whereCondition", getTalentDetailWhereConditionSqlByCompany(searchDto, whereParamMap));
        sql = StrUtil.format(sql, map);
        Query query = entityManager.createNativeQuery(sql, RecruitingKpiTalentDetailVO.class);
        setListWithPage(pageable, whereParamMap, query);
        List<RecruitingKpiTalentDetailVO> voList = query.getResultList();
        stopWatch.stop();
        log.info(" searchTalentDetailList time = {} ms= \n {}", stopWatch.getTotalTimeMillis(), stopWatch.prettyPrint());
        return voList;
    }


    public Long searchTalentDetailCount(RecruitingKpiTalentDetailSearchDto searchDto) {
        StopWatch stopWatch = new StopWatch("searchTalentDetailCount");
        stopWatch.start();
        String sql;
        Map<String, Object> whereParamMap = new HashMap<>(16);
        Map<String, String> map = new HashMap<>(16);
        sql = """
            SELECT
                count(distinct t.id)
            FROM
                talent t
                FORCE INDEX (idx_talent_tenant_id_created_date)
                INNER JOIN talent_recruitment_process trp ON trp.talent_id = t.id
                INNER JOIN job j on j.id = trp.job_id
                INNER JOIN company c on c.id = j.company_id
                INNER JOIN company_user_relation ul ON ul.company_id = c.id
                INNER JOIN permission_user_team put ON put.user_id = ul.user_id and put.is_primary = 1
                INNER JOIN permission_team pt on pt.id = put.team_id
                left JOIN recruitment_process rp ON rp.id = j.recruitment_process_id
                left join company_industry_relation cir on cir.company_id = c.id
                LEFT JOIN talent_additional_info tai ON tai.id = t.additional_info_id
            WHERE
            t.tenant_id = :tenantId {dateSearchSql}
            {whereCondition}
            """;
        map.put("dateSearchSql", " and t.created_date BETWEEN :startDate AND :endDate ");
        whereParamMap.put("tenantId", searchDto.getSearchTenantId());
        whereParamMap.put("startDate", searchDto.getStartDateUtc());
        whereParamMap.put("endDate", searchDto.getEndDateUtc());
        map.put("whereCondition", getTalentDetailWhereConditionSqlByCompany(searchDto, whereParamMap));
        sql = StrUtil.format(sql, map);
        Query countQ = entityManager.createNativeQuery(sql);
        Optional.of(whereParamMap).ifPresent(m -> m.forEach(countQ::setParameter));
        long count = Long.parseLong(String.valueOf(countQ.getSingleResult()));
        stopWatch.stop();
        log.info("searchTalentDetailCount time = {} ms= \n {}", stopWatch.getTotalTimeMillis(), stopWatch.prettyPrint());
        return count;
    }

    public List<? extends RecruitingKpiApplicationBaseDetailVO> searchApplicationDetailList(RecruitingKpiApplicationDetailSearchDto searchDto, Pageable pageable) {
        StopWatch stopWatch = new StopWatch("searchApplicationDetailList");
        stopWatch.start();
        String sql = """
                {withSql}
                SELECT
                	node.id node_id,
                	t.id talent_id,
                	trp.id talent_recruitment_process_id,
                	t.full_name full_name,
                	count( DISTINCT tn.id ) talent_notes,
                	{selectFields}
                	j.title job_title,
                	j.id job_id,
                	j.code job_code,
                    j.pteam_id,
                	c.full_business_name company_name,
                	c.id company_id,
                    rp.job_type job_type,
                	j.status job_status,
                	j.flexible_location,
                    CASE WHEN trpn.node_status = 1 THEN trpn.node_type WHEN trpn.node_status = 4 THEN -1 END workflow_status,
                	GROUP_CONCAT( distinct jl.format_location SEPARATOR ";") job_location,
                	group_concat( DISTINCT am.user_id ) am,
                	group_concat( DISTINCT re.user_id ) recruiter,
                	group_concat( DISTINCT so.user_id ) sourcer,
                	group_concat( DISTINCT ac.user_id ) ac,
                	group_concat( DISTINCT dm.user_id ) dm,
                	group_concat( DISTINCT owner.user_id ) owner,
                	group_concat( DISTINCT concat(coam.user_id, '-', coam.country)) co_am,
                    group_concat( DISTINCT salesleadowner.user_id ) sales_lead_owner,
                	group_concat( DISTINCT bdowner.user_id ) bd_owner,
                	node.last_update_user_id last_modified_by,
                	node.last_modified_date,
                	if(resign.id is null, false, true) as resigned,
                	if(star.id is null, false, true) as converted_to_fte  
                FROM
                	{fromTables} node
                	INNER JOIN talent_recruitment_process trp ON trp.id = node.talent_recruitment_process_id
                	LEFT JOIN talent_recruitment_process_resignation resign ON trp.id = resign.talent_recruitment_process_id
                	left join start star on resign.talent_recruitment_process_id = star.talent_recruitment_process_id and star.start_type=5
                	INNER JOIN talent t ON t.id = trp.talent_id
                	INNER JOIN job j ON j.id = trp.job_id
                    INNER JOIN recruitment_process rp on rp.id = j.recruitment_process_id
                	INNER JOIN company c ON j.company_id = c.id
                    INNER JOIN company_user_relation ul ON ul.company_id = c.id
                	INNER JOIN permission_user_team put ON put.user_id = ul.user_id and put.is_primary = 1
                	INNER JOIN permission_team pt ON pt.id = put.team_id AND pt.team_category_id IN (15,20)
                	{leftJoinTables}
                    {withLeftSql}                                                                                        
                	LEFT JOIN talent_recruitment_process_kpi_user am ON am.talent_recruitment_process_id = trp.id AND am.user_role = 0
                	LEFT JOIN talent_recruitment_process_kpi_user re ON re.talent_recruitment_process_id = trp.id AND re.user_role = 1
                	LEFT JOIN talent_recruitment_process_kpi_user so ON so.talent_recruitment_process_id = trp.id AND so.user_role = 2
                	LEFT JOIN talent_recruitment_process_kpi_user ac ON ac.talent_recruitment_process_id = trp.id AND ac.user_role = 5
                	LEFT JOIN talent_recruitment_process_kpi_user dm ON dm.talent_recruitment_process_id = trp.id AND dm.user_role = 3
                	LEFT JOIN talent_recruitment_process_kpi_user owner ON owner.talent_recruitment_process_id = trp.id AND owner.user_role = 4
                	LEFT JOIN talent_recruitment_process_kpi_user coam ON coam.talent_recruitment_process_id = trp.id AND coam.user_role = 7
                    LEFT JOIN talent_recruitment_process_kpi_user salesleadowner ON salesleadowner.talent_recruitment_process_id = trp.id AND salesleadowner.user_role = 9
                    LEFT JOIN talent_recruitment_process_kpi_user bdowner ON bdowner.talent_recruitment_process_id = trp.id AND bdowner.user_role = 8
                	LEFT JOIN talent_note tn ON tn.talent_id = t.id
                	LEFT JOIN job_location jl ON jl.job_id = j.id
                WHERE
                	{whereCondition}
                GROUP BY
                	node.id
                	{orderBy}
                """;
        Map<String, Object> whereParamMap = new HashMap<>(16);
        sql = setApplicationSqlByCompany(sql, searchDto, whereParamMap);
        Class<? extends RecruitingKpiApplicationBaseDetailVO> clazz = searchDto.getApplicationDetailClass();
        Query query = entityManager.createNativeQuery(sql);
        query.unwrap(SQLQuery.class).setResultTransformer(Transformers.ALIAS_TO_ENTITY_MAP);
        setListWithPage(pageable, whereParamMap, query);
        List<Map<String, Object>> mapList = query.getResultList();
        List<? extends RecruitingKpiApplicationBaseDetailVO> voList = MapToEntityUtil.convertEntity(mapList, clazz);
        stopWatch.stop();
        voList.forEach(x -> {
            if (StringUtils.isNotBlank(x.getCoAm())) {
                List<UserCountryVO> userCountryList = new ArrayList<>();
                Arrays.stream(x.getCoAm().split(",")).forEach(v -> {
                    String[] amCountry = v.split("-");
                    UserCountryVO vo = new UserCountryVO();
                    vo.setUserId(Long.valueOf(amCountry[0]));
                    vo.setCountryId(amCountry[1]);
                    userCountryList.add(vo);
                });
                x.setCoAmList(userCountryList);
            }
        });
        log.info(" searchApplicationDetailList time = {} ms= \n {}", stopWatch.getTotalTimeMillis(), stopWatch.prettyPrint());
        return voList.stream().map(d -> d.setResigned(BooleanUtils.isTrue(d.getConvertedToFte()) ? Boolean.FALSE : d.getResigned())).toList();
    }

    public Long searchApplicationDetailCount(RecruitingKpiApplicationDetailSearchDto searchDto) {
        StopWatch stopWatch = new StopWatch("searchApplicationDetailCount");
        stopWatch.start();
        String sql = """
                {withSql}
                SELECT
                	count(distinct node.id)
                FROM
                	{fromTables} node
                	INNER JOIN talent_recruitment_process trp ON trp.id = node.talent_recruitment_process_id
                	LEFT JOIN talent_recruitment_process_resignation resign ON trp.id = resign.talent_recruitment_process_id
                	INNER JOIN talent t ON t.id = trp.talent_id
                	INNER JOIN job j ON j.id = trp.job_id
                	INNER JOIN recruitment_process rp on rp.id = j.recruitment_process_id
                	INNER JOIN company c ON j.company_id = c.id
	                INNER JOIN company_user_relation ul ON ul.company_id = c.id
                	INNER JOIN permission_user_team put ON put.user_id = ul.user_id and put.is_primary = 1
                	INNER JOIN permission_team pt ON pt.id = put.team_id AND pt.team_category_id IN (15,20)
                	{leftJoinTables}
	                {withLeftSql}
                	LEFT JOIN talent_recruitment_process_kpi_user am ON am.talent_recruitment_process_id = trp.id AND am.user_role = 0
                	LEFT JOIN talent_recruitment_process_kpi_user re ON re.talent_recruitment_process_id = trp.id AND re.user_role = 1
                	LEFT JOIN talent_recruitment_process_kpi_user so ON so.talent_recruitment_process_id = trp.id AND so.user_role = 2
                	LEFT JOIN talent_recruitment_process_kpi_user ac ON ac.talent_recruitment_process_id = trp.id AND ac.user_role = 5
                	LEFT JOIN talent_recruitment_process_kpi_user dm ON dm.talent_recruitment_process_id = trp.id AND dm.user_role = 3
                	LEFT JOIN talent_recruitment_process_kpi_user owner ON owner.talent_recruitment_process_id = trp.id AND owner.user_role = 4
                	LEFT JOIN talent_recruitment_process_kpi_user coam ON coam.talent_recruitment_process_id = trp.id AND coam.user_role = 7
                    LEFT JOIN talent_recruitment_process_kpi_user salesleadowner ON salesleadowner.talent_recruitment_process_id = trp.id AND salesleadowner.user_role = 9
                    LEFT JOIN talent_recruitment_process_kpi_user bdowner ON bdowner.talent_recruitment_process_id = trp.id AND bdowner.user_role = 8
                	LEFT JOIN job_location jl ON jl.job_id = j.id
                WHERE
                	{whereCondition}
                """;
        Map<String, Object> whereParamMap = new HashMap<>(16);
        sql = setApplicationSqlByCompany(sql, searchDto, whereParamMap);
        Query query = entityManager.createNativeQuery(sql);
        setListWithPage(null, whereParamMap, query);
        long count = Long.parseLong(String.valueOf(query.getSingleResult()));
        stopWatch.stop();
        log.info(" searchApplicationDetailCount time = {} ms= \n {}", stopWatch.getTotalTimeMillis(), stopWatch.prettyPrint());
        return count;
    }

    public List<RecruitingKpiJobNoteDetailVO> searchJobNoteDetailList(RecruitingKpiJobNoteDetailSearchDto searchDto, Pageable pageable, TeamDataPermissionRespDTO teamDTO) {
        StopWatch stopWatch = new StopWatch("searchJobNoteDetailList");
        stopWatch.start();
        String sql = """
                SELECT
                	jn.id,
                	jn.job_id job_id,
                	j.title job_title,
	                j.pteam_id,
                	jn.note note,
                	jn.puser_id created_by,
                	jn.last_update_user_id last_modified_by,
                	jn.created_date,
                	jn.last_modified_date
                FROM
                	job_note jn
                	inner join job j on j.id = jn.job_id
                	INNER JOIN company c ON c.id = j.company_id
	                INNER JOIN company_user_relation ul ON ul.company_id = c.id
                	INNER JOIN permission_user_team put ON put.user_id = ul.user_id and put.is_primary = 1
                    left join company_industry_relation cir on cir.company_id = c.id
                    left JOIN recruitment_process rp ON rp.id = j.recruitment_process_id
                	where 1=1 and jn.visible = 1
                	{whereClause}
                	group by jn.id
                	{orderBy}
                """;
        Map<String, Object> whereParamMap = new HashMap<>(16);
        Map<String, String> map = new HashMap<>();
        map.put("whereClause", getWhereConditionForJobNoteByCompany(searchDto, whereParamMap, teamDTO));
        map.put("orderBy", searchDto.getOrderBySql());
        sql = StrUtil.format(sql, map);
        Query query = entityManager.createNativeQuery(sql, RecruitingKpiJobNoteDetailVO.class);
        setListWithPage(pageable, whereParamMap, query);
        List<RecruitingKpiJobNoteDetailVO> voList = query.getResultList();
        stopWatch.stop();
        log.info(" searchJobNoteDetailList time = {} ms= \n {}", stopWatch.getTotalTimeMillis(), stopWatch.prettyPrint());
        return voList;
    }

    public Long searchJobNoteDetailCount(RecruitingKpiJobNoteDetailSearchDto searchDto, TeamDataPermissionRespDTO teamDTO) {
        StopWatch stopWatch = new StopWatch("searchJobNoteDetailCount");
        stopWatch.start();
        String sql = """ 
                SELECT
                	count(distinct jn.id)
                FROM
                	job_note jn
                	inner join job j on j.id = jn.job_id
                	INNER JOIN company c ON c.id = j.company_id
	                INNER JOIN company_user_relation ul ON ul.company_id = c.id
                	INNER JOIN permission_user_team put ON put.user_id = ul.user_id and put.is_primary = 1
	                left join company_industry_relation cir on cir.company_id = c.id
	                left JOIN recruitment_process rp ON rp.id = j.recruitment_process_id
                where 1=1 and jn.visible = 1
                	{whereClause}
                """;
        Map<String, Object> whereParamMap = new HashMap<>(16);
        Map<String, String> map = new HashMap<>();
        map.put("whereClause", getWhereConditionForJobNoteByCompany(searchDto, whereParamMap, teamDTO));
        sql = StrUtil.format(sql, map);
        Query query = entityManager.createNativeQuery(sql);
        setListWithPage(null, whereParamMap, query);
        long count = Long.parseLong(String.valueOf(query.getSingleResult()));
        stopWatch.stop();
        log.info(" searchJobNoteDetailCount time = {} ms= \n {}", stopWatch.getTotalTimeMillis(), stopWatch.prettyPrint());
        return count;
    }

    private String getWhereConditionForJobNoteByCompany(RecruitingKpiJobNoteDetailSearchDto searchDto, Map<String, Object> whereParamMap, TeamDataPermissionRespDTO teamDTO) {
        StringBuilder sb = new StringBuilder();
        sb.append(" and j.tenant_id = :tenantId ");
        whereParamMap.put("tenantId", SecurityUtils.getTenantId());
        appendDetailCommonCondition(searchDto.getCompanyId(), searchDto.getJobId(), searchDto.getUserId(), searchDto.getTeamId(), sb, whereParamMap);
        //company search
        appendCompanySearch(sb, searchDto, whereParamMap);
        appendTeamLimitByCompany(sb, searchDto, whereParamMap);
        //detail
        searchDto.appendDetailCondition(sb, whereParamMap);
        if (ObjectUtil.isNotEmpty(searchDto.getJob()) && CollUtil.isNotEmpty(searchDto.getJob().getTypeList())) {
            sb.append(" and rp.job_type in :jobType ");
            whereParamMap.put("jobType", searchDto.getJob().getTypeList().stream().map(JobType::toDbValue).toList());
        }
        return sb.toString();
    }

    private String setApplicationSqlByCompany(String sql, RecruitingKpiApplicationDetailSearchDto searchDto, Map<String, Object> whereParamMap) {
        Map<String, String> map = new HashMap<>(16);
        map.put("withSql", " ");
        map.put("withLeftSql", " ");
        if (searchDto.getApplicationStatusType() == RecruitingKpiApplicationStatusType.CURRENT) {
            String withSql = """
                    WITH interview_max as (
                                    select
                                    id,
                                    max(progress) progress,
                                    talent_recruitment_process_id
                                    from talent_recruitment_process_interview
                                    group by talent_recruitment_process_id
                                    )
                    """;
            String withLeftSql = """
                    left join interview_max max on max.talent_recruitment_process_id = node.talent_recruitment_process_id
                    """;
            map.put("withSql", withSql);
            map.put("withLeftSql", withLeftSql);
        }
        map.put("selectFields", searchDto.getSelectFields(searchDto));
        map.put("fromTables", searchDto.getFromTables(searchDto.getReportApplicationStatus()));
        map.put("leftJoinTables", getLeftJobTablesForApplicationByCompany(searchDto));
        map.put("whereCondition", getWhereConditionForApplication(searchDto, whereParamMap));
        map.put("orderBy", searchDto.getOrderBy());
        return StrUtil.format(sql, map);
    }

    private String getWhereConditionForApplication(RecruitingKpiApplicationDetailSearchDto searchDto, Map<String, Object> whereParamMap) {
        StringBuilder sb = new StringBuilder();
        ReportApplicationStatus reportApplicationStatus = searchDto.getReportApplicationStatus();
        boolean eventFlag = searchDto.getDateType() == RecruitingKpiDateType.EVENT;
        boolean currentFlag = searchDto.getApplicationStatusType() == RecruitingKpiApplicationStatusType.CURRENT;
        sb.append(" trp.tenant_id = :tenantId ");
        whereParamMap.put("tenantId", SecurityUtils.getTenantId());
        String fieldColumn = " node.created_date ";
        whereParamMap.put("startDate", searchDto.getStartDateUtc());
        whereParamMap.put("endDate", searchDto.getEndDateUtc());
        if (eventFlag) {
            if (reportApplicationStatus == ReportApplicationStatus.SUBMIT_TO_CLIENT) {
                fieldColumn = "node.submit_time_format";
                whereParamMap.put("startDate", searchDto.getStartDate());
                whereParamMap.put("endDate", searchDto.getEndDate());
            } else if (List.of(ReportApplicationStatus.INTERVIEW_FIRST,
                            ReportApplicationStatus.INTERVIEW_SECOND,
                            ReportApplicationStatus.INTERVIEW_FINAL,
                            ReportApplicationStatus.TWO_OR_MORE_INTERVIEW,
                            ReportApplicationStatus.INTERVIEW)
                    .contains(reportApplicationStatus)) {
                fieldColumn = " node.from_time_utc ";
            } else if (reportApplicationStatus == ReportApplicationStatus.ON_BOARD) {
                fieldColumn = "trpod.onboard_date";
                whereParamMap.put("startDate", searchDto.getStartDate());
                whereParamMap.put("endDate", searchDto.getEndDate());
            }
        }
        sb.append(" and ").append(fieldColumn).append(" BETWEEN :startDate AND :endDate ");
        appendDetailCommonCondition(searchDto.getCompanyId(), searchDto.getJobId(), searchDto.getUserId(), searchDto.getTeamId(), sb, whereParamMap);
        appendCommonUserIdAndTeamId(searchDto, sb, whereParamMap);
        appendCompanySearch(sb, searchDto, whereParamMap);
        appendTeamLimitByCompany(sb, searchDto, whereParamMap);
        if (List.of(ReportApplicationStatus.INTERVIEW_FIRST, ReportApplicationStatus.INTERVIEW_SECOND, ReportApplicationStatus.TWO_OR_MORE_INTERVIEW, ReportApplicationStatus.INTERVIEW_FINAL).contains(searchDto.getReportApplicationStatus())) {
            switch (searchDto.getReportApplicationStatus()) {
                case INTERVIEW_FIRST -> sb.append(" and node.progress = 1 ").append(currentFlag? " and max.progress = 1 ": "");
                case INTERVIEW_SECOND -> sb.append(" and node.progress = 2 ").append(currentFlag? " and max.progress = 2 ": "");
                case INTERVIEW_FINAL -> sb.append(" and node.final_round = 1 ");
                case TWO_OR_MORE_INTERVIEW -> sb.append(" and node.progress >= 2 and node.final_round != 1 ");
            }
        }
        searchDto.appendDetailCondition(sb, whereParamMap);
        if (ObjectUtil.isNotEmpty(searchDto.getJob()) && CollUtil.isNotEmpty(searchDto.getJob().getTypeList())) {
            sb.append(" and rp.job_type in :jobType ");
            whereParamMap.put("jobType", searchDto.getJob().getTypeList().stream().map(JobType::toDbValue).toList());
        }
        return sb.toString();
    }

    private String getLeftJobTablesForApplicationByCompany(RecruitingKpiApplicationDetailSearchDto searchDto) {
        ReportApplicationStatus reportApplicationStatus = searchDto.getReportApplicationStatus();
        StringBuilder sb = new StringBuilder();
        boolean isCurrentFlag = searchDto.getApplicationStatusType() == RecruitingKpiApplicationStatusType.CURRENT;
        if (isCurrentFlag) {
            sb.append(getCurrentNodeTypeForCurrentByApplicationStatus(searchDto.getReportApplicationStatus()));
        } else {
            sb.append(" INNER JOIN talent_recruitment_process_node trpn ON trp.id = trpn.talent_recruitment_process_id AND trpn.node_status IN ( 1, 4 ) ");
        }
        if (reportApplicationStatus == ReportApplicationStatus.OFFER_ACCEPT
                || (reportApplicationStatus == ReportApplicationStatus.ON_BOARD && !isCurrentFlag)) {
            sb.append(" left join talent_recruitment_process_onboard_date trpod on trpod.talent_recruitment_process_id = trp.id ");
        }
        if (List.of(ReportApplicationStatus.INTERVIEW,
                ReportApplicationStatus.INTERVIEW_FIRST,
                ReportApplicationStatus.INTERVIEW_SECOND,
                ReportApplicationStatus.TWO_OR_MORE_INTERVIEW,
                ReportApplicationStatus.INTERVIEW_APPOINTMENTS,
                ReportApplicationStatus.INTERVIEW_FINAL).contains(reportApplicationStatus)) {
            sb.append(" left join talent_recruitment_process_interview trpi on trpi.talent_recruitment_process_id = trp.id ");
        }
        return sb.toString();
    }


    private String getJobDetailWhereConditionSqlByCompany(RecruitingKpiJobDetailSearchDto searchDto, Map<String, Object> whereParamMap) {
        StringBuilder sb = new StringBuilder();
        sb.append(" and j.tenant_id = :tenantId ");
        whereParamMap.put("tenantId", SecurityUtils.getTenantId());
        boolean createFlag = searchDto.getDateType() == RecruitingKpiDateType.ADD;
        String dateField = createFlag? " j.created_date ": " j.start_date_format ";
        sb.append(" and ").append(dateField).append(" BETWEEN :startDate AND :endDate ");
        whereParamMap.put("startDate", createFlag? searchDto.getStartDateUtc(): searchDto.getStartDate());
        whereParamMap.put("endDate", createFlag? searchDto.getEndDateUtc(): searchDto.getEndDate());
        appendDetailCommonCondition(searchDto.getCompanyId(), searchDto.getJobId(), searchDto.getUserId(), searchDto.getTeamId(), sb, whereParamMap);
        appendCommonUserIdAndTeamId(searchDto, sb, whereParamMap);
        //search company
        appendCompanySearch(sb, searchDto, whereParamMap);
        //job detail
        searchDto.appendDetailCondition(sb, whereParamMap);
        if (ObjectUtil.isNotEmpty(searchDto.getJob()) && CollUtil.isNotEmpty(searchDto.getJob().getTypeList())) {
            sb.append(" and rp.job_type in :jobType ");
            whereParamMap.put("jobType", searchDto.getJob().getTypeList().stream().map(JobType::toDbValue).toList());
        }
        appendTeamLimitByCompany(sb, searchDto, whereParamMap);
        //search permission team
        appendPermissionTeamSearch(sb);
        return sb.toString();
    }

    private String getTalentDetailWhereConditionSqlByCompany(RecruitingKpiTalentDetailSearchDto searchDto, Map<String, Object> whereParamMap) {
        StringBuilder sb = new StringBuilder();
        appendDetailCommonCondition(searchDto.getCompanyId(), searchDto.getJobId(), searchDto.getUserId(), searchDto.getTeamId(), sb, whereParamMap);
        appendCommonUserIdAndTeamId(searchDto, sb, whereParamMap);
        //company search
        appendCompanySearch(sb, searchDto, whereParamMap);
        searchDto.appendDetailCondition(sb, whereParamMap);
        if (ObjectUtil.isNotEmpty(searchDto.getJob()) && CollUtil.isNotEmpty(searchDto.getJob().getTypeList())) {
            sb.append(" and rp.job_type in :jobType ");
            whereParamMap.put("jobType", searchDto.getJob().getTypeList().stream().map(JobType::toDbValue).toList());
        }
        appendTeamLimitByCompany(sb, searchDto, whereParamMap);
        //search permission team
        appendPermissionTeamSearch(sb);
        return sb.toString();
    }


    public List<String> getKpiReportCountry(Long tenantId) {
        String sql = """
                    SELECT
                        DISTINCT cl.official_country
                    FROM
                        company c
                    INNER JOIN company_location cl ON cl.company_id = c.id
                    WHERE c.tenant_id = :tenantId AND cl.official_country IS NOT NULL
                    ORDER BY cl.official_country ASC
                """;

        List<Object[]> result = entityManager.createNativeQuery(sql)
                .setParameter("tenantId", tenantId)
                .getResultList();

        // 处理结果，将 Object[] 转换为 String
        List<String> countries = new ArrayList<>();
        for (Object row : result) {
            countries.add((String) row);
        }
        return countries;

    }

    public List<RecruitingKpiCompanyNoteDetailVO> searchCompanyNoteDetailList(RecruitingKpiCompanyNoteDetailSearchDto searchDto, Pageable pageable) {
        String sql = """
                SELECT
                	ccn.id,
                	ccn.company_id,
                	c.full_business_name company_name,
	                group_concat( distinct concat(t.full_name) ) contact_name,
                	group_concat( distinct concat(t.id, "-", t.full_name) ) contact_info,
                	group_concat( distinct concat(t.id) ) contact_id,
	                ccn.last_contact_date contact_date,
                	ccn.note,
                	ccn.puser_id created_by_id
                FROM
                	company_client_note ccn
                	INNER JOIN company c ON c.id = ccn.company_id
                	left JOIN company_client_note_contact_relation ccncr ON ccncr.client_note_id = ccn.id
                	left JOIN company_sales_lead_client_contact cslcc ON cslcc.id = ccncr.client_contact_id
                	left JOIN talent t ON t.id = cslcc.talent_id
                WHERE
                	ccn.company_id = :companyId and ccn.deleted = 0
                GROUP BY
                	ccn.id
                """ + " " + searchDto.getOrderBySql();
        Query query = entityManager.createNativeQuery(sql, RecruitingKpiCompanyNoteDetailVO.class);
        Map<String, Object> whereParamMap = new HashMap<>();
        whereParamMap.put("companyId", searchDto.getCompanyId());
        setListWithPage(pageable, whereParamMap, query);
        return query.getResultList();
    }

    public Long searchCompanyNoteDetailCount(RecruitingKpiCompanyNoteDetailSearchDto searchDto) {
        String sql = """
                SELECT
                	count(distinct ccn.id)
                FROM
                	company_client_note ccn
                	INNER JOIN company c ON c.id = ccn.company_id
                	left JOIN company_client_note_contact_relation ccncr ON ccncr.client_note_id = ccn.id
                	left JOIN company_sales_lead_client_contact cslcc ON cslcc.id = ccncr.client_contact_id
                	left JOIN talent t ON t.id = cslcc.talent_id
                WHERE
                	ccn.company_id = :companyId 
                """ + " " + searchDto.getOrderBySql();
        Query countQ = entityManager.createNativeQuery(sql).setParameter("companyId", searchDto.getCompanyId());
        return Long.parseLong(String.valueOf(countQ.getSingleResult()));
    }
}
