package com.altomni.apn.report.service.recruiting.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.Pair;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.ReflectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.alibaba.nacos.common.utils.ExceptionUtil;
import com.altomni.apn.common.aop.confidential.ProcessConfidentialTalent;
import com.altomni.apn.common.aop.confidential.TalentConfidentialAspect;
import com.altomni.apn.common.domain.dict.EnumMotivation;
import com.altomni.apn.common.domain.enumeration.talent.TalentNoteType;
import com.altomni.apn.common.dto.application.dashboard.MyCandidateStatusFilter;
import com.altomni.apn.common.dto.enums.EnumDictDTO;
import com.altomni.apn.common.dto.http.HttpResponse;
import com.altomni.apn.common.dto.permission.TeamDataPermissionRespDTO;
import com.altomni.apn.common.dto.recruiting.RecruitingKpiDetailBaseDto;
import com.altomni.apn.common.dto.recruiting.RecruitingKpiReportSearchDto;
import com.altomni.apn.common.dto.user.UserBriefDTO;
import com.altomni.apn.common.enumeration.recruiting.RecruitingKpiGroupByFieldType;
import com.altomni.apn.common.service.http.HttpService;
import com.altomni.apn.common.utils.DateUtil;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.common.vo.recruiting.KpiReportCompanyUpgradeToClientVO;
import com.altomni.apn.finance.service.vo.invoice.UserCountryVO;
import com.altomni.apn.report.domain.enumeration.ReportApplicationStatus;
import com.altomni.apn.report.domain.vo.*;
import com.altomni.apn.report.dto.*;
import com.altomni.apn.report.repository.RecruitingKpiCompanyRepository;
import com.altomni.apn.report.repository.RecruitingKpiUserExtendRepository;
import com.altomni.apn.report.service.company.CompanyService;
import com.altomni.apn.report.service.recruiting.RecruitingKpiByUserService;
import com.altomni.apn.report.util.ExcelUtil;
import lombok.extern.slf4j.Slf4j;
import okhttp3.Headers;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpHeaders;
import org.springframework.security.core.context.SecurityContext;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Service;
import org.springframework.util.StopWatch;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Slf4j
@Service("recruitingKpiByUserService")
public class RecruitingKpiByUserServiceImpl extends RecruitingKpiBaseServiceImpl implements RecruitingKpiByUserService {

    @Resource
    private RecruitingKpiUserExtendRepository recruitingKpiUserExtendRepository;

    @Resource
    private RecruitingKpiCompanyRepository recruitingKpiCompanyRepository;

    @Value("${application.crmUrl}")
    private String crmUrl;

    @Resource
    private HttpService httpService;

    @Resource
    private CompanyService companyService;

    // 自注入代理对象，用于调用带有AOP注解的方法
    @Lazy
    @Resource
    private RecruitingKpiByUserService self;

    @Override
    public Page<RecruitingKpiJobDetailVO> searchJobDetailPage(RecruitingKpiJobDetailSearchDto searchDto, Pageable pageable, boolean withCountFlag) {
        //获取是有职位
        TeamDataPermissionRespDTO teamDTO = getPermissionDTOAndSetCommonParam(searchDto);
        SecurityContext context = SecurityContextHolder.getContext();
        //只有传jobId 的时候才使用,唯一的情况就是使用了 groupBy job 的时候
        CompletableFuture<List<RecruitingKpiJobDetailVO>> dataFuture = CompletableFuture.supplyAsync(() -> {
            SecurityContextHolder.setContext(context);
            return recruitingKpiUserExtendRepository.searchJobDetailList(searchDto, pageable, teamDTO);
        }, executorService);

        CompletableFuture<Long> countFuture = CompletableFuture.supplyAsync(() -> 0L);
        if (withCountFlag) {
            countFuture = CompletableFuture.supplyAsync(() -> {
                SecurityContextHolder.setContext(context);
                return recruitingKpiUserExtendRepository.searchJobDetailCount(searchDto, teamDTO);
            }, executorService);
        }
        List<RecruitingKpiJobDetailVO> voList = dataFuture.join();
        if (withCountFlag) {
            voList.parallelStream().forEach(vo -> {
                if (searchDto.getPermissionRespDTO() != null && Objects.equals(searchDto.getPermissionRespDTO().getTeamIdForPrivateJob(), vo.getPteamId())) {
                    vo.setPrivateJob(true);
                }
            });
        }
        return new PageImpl<>(voList, Pageable.unpaged(), countFuture.join());
    }

    @Override
    public void exportJobDetailList(RecruitingKpiJobDetailSearchDto searchDto, HttpServletResponse response) {
        StopWatch stopWatch = new StopWatch("exportJobDetailList");
        stopWatch.start("1. search data");
        List<RecruitingKpiJobDetailVO> voList = searchJobDetailPage(searchDto, null, false).getContent();
        stopWatch.stop();
        stopWatch.start("2. 实体转换");
        List<RecruitingKpiJobDetailENVO> list = new ArrayList<>();
        voList.forEach(vo -> {
            try {
                RecruitingKpiJobDetailENVO detailVo = new RecruitingKpiJobDetailENVO();
                BeanUtil.copyProperties(vo, detailVo, true);
                if (ObjectUtil.isNotEmpty(vo.getPostingDate()) && StrUtil.isNotBlank(searchDto.getTimezone())) {
                    ReflectUtil.setFieldValue(detailVo, "postTingTimeFormat", formatDate(vo.getPostingDate(), searchDto.getTimezone(), "yyyy-MM-dd"));
                }
                if (ObjectUtil.isNotEmpty(vo.getOpenDate()) && StrUtil.isNotBlank(searchDto.getTimezone())) {
                    ReflectUtil.setFieldValue(detailVo, "openTimeFormat", formatDate(vo.getOpenDate(), searchDto.getTimezone(), "yyyy-MM-dd"));
                }
                if (ObjectUtil.isNotEmpty(vo.getTenantWebsitePostingDate()) && StrUtil.isNotBlank(searchDto.getTimezone())) {
                    ReflectUtil.setFieldValue(detailVo, "tenantWebsitePostingDateFormat", formatDate(vo.getTenantWebsitePostingDate(), searchDto.getTimezone(), "yyyy-MM-dd"));
                }
                if (vo.getContractDuration() != null){
                    detailVo.setContractDuration(vo.getContractDuration() + " Days");
                }
                list.add(detailVo);
            } catch (Exception e) {
                log.error("exportJobDetailList error {} ", ExceptionUtil.getAllExceptionMsg(e));
            }
        });
        stopWatch.stop();
        stopWatch.start("3. 输出 excel");
        ExcelUtil.downloadExcel(response, RecruitingKpiJobDetailENVO.class, list, "",  "JobDetail.xlsx", false);
        stopWatch.stop();
        log.info(" exportJobDetailList by user time = {} ms= \n {}", stopWatch.getTotalTimeMillis(), stopWatch.prettyPrint());
    }

    @ProcessConfidentialTalent
    @Override
    public Page<RecruitingKpiTalentDetailVO> searchTalentDetailPage(RecruitingKpiTalentDetailSearchDto searchDto, Pageable pageable, boolean withCountFlag) {
        TeamDataPermissionRespDTO teamDTO = getPermissionDTOAndSetCommonParam(searchDto);
        SecurityContext context = SecurityContextHolder.getContext();
        CompletableFuture<List<RecruitingKpiTalentDetailVO>> dataFuture = CompletableFuture.supplyAsync(() -> {
            SecurityContextHolder.setContext(context);
            return recruitingKpiUserExtendRepository.searchTalentDetailList(searchDto, pageable, teamDTO);
        }, executorService);
        CompletableFuture<Long> countFuture = CompletableFuture.supplyAsync(() -> 0L);
        if (withCountFlag) {
            countFuture = CompletableFuture.supplyAsync(() -> {
                SecurityContextHolder.setContext(context);
                return recruitingKpiUserExtendRepository.searchTalentDetailCount(searchDto, teamDTO);
            }, executorService);
        }
        List<RecruitingKpiTalentDetailVO> voList = dataFuture.join();
        return new PageImpl<>(voList, Pageable.unpaged(), countFuture.join());
    }

    @Override
    public void exportTalentDetailList(RecruitingKpiTalentDetailSearchDto searchDto, HttpServletResponse response) {
        StopWatch stopWatch = new StopWatch("exportTalentDetailList");
        stopWatch.start("1. search data");
        CompletableFuture<Map<Long, UserBriefDTO>> userMapFuture = getUserMapFuture();
        Map<Integer, EnumMotivation> enumMotivationMap = enumMotivationService.findAllEnumMotivation().stream().collect(Collectors.toMap(EnumMotivation::getId, a -> a));
        // 使用代理对象调用，确保AOP切面生效
        List<RecruitingKpiTalentDetailVO> voList = self.searchTalentDetailPage(searchDto, null, false).getContent();
        stopWatch.stop();
        stopWatch.start("2. 实体转换");
        List<RecruitingKpiTalentDetailENVO> list = new ArrayList<>();
        Map<Long, UserBriefDTO> map = userMapFuture.join();
        voList.forEach(vo -> {
            try {
                RecruitingKpiTalentDetailENVO detailVo = new RecruitingKpiTalentDetailENVO();
                BeanUtil.copyProperties(vo, detailVo, true);
                setCreatedAndLastModified(vo.getCreatedBy(), vo.getCreatedDate(), vo.getLastModifiedBy(), vo.getLastModifiedDate(), searchDto.getTimezone(), detailVo, map);
                setJobSearchStatusDisplay(enumMotivationMap, vo.getJobSearchStatus(), detailVo, searchDto.getType());

                // 如果是保密候选人，将所有显示字段设置为 ***
                if (vo.getConfidentialTalentViewAble() != null && !vo.getConfidentialTalentViewAble()) {
                    ExcelUtil.maskConfidentialTalentData(detailVo);
                }

                list.add(detailVo);
            } catch (Exception e) {
                log.error("exportTalentDetailList error {} ", ExceptionUtil.getAllExceptionMsg(e));
            }
        });
        stopWatch.stop();
        stopWatch.start("3. 输出 excel");
        ExcelUtil.downloadExcel(response, RecruitingKpiTalentDetailENVO.class, list, "",  "talentDetail.xlsx", false);
        stopWatch.stop();
        log.info(" exportTalentDetailList by user time = {} ms= \n {}", stopWatch.getTotalTimeMillis(), stopWatch.prettyPrint());
    }

    @ProcessConfidentialTalent
    @Override
    public Page<? extends RecruitingKpiApplicationBaseDetailVO> searchApplicationDetailPage(RecruitingKpiApplicationDetailSearchDto searchDto, Pageable pageable, boolean withCountFlag) {
        TeamDataPermissionRespDTO teamDTO = getPermissionDTOAndSetCommonParam(searchDto);
        SecurityContext context = SecurityContextHolder.getContext();
        CompletableFuture<List<? extends RecruitingKpiApplicationBaseDetailVO>> dataFuture = CompletableFuture.supplyAsync(() -> {
            SecurityContextHolder.setContext(context);
            return recruitingKpiUserExtendRepository.searchApplicationDetailList(searchDto, pageable, teamDTO);
        }, executorService);
        CompletableFuture<Long> countFuture = CompletableFuture.supplyAsync(() -> 0L);
        if (withCountFlag) {
            countFuture = CompletableFuture.supplyAsync(() -> {
                SecurityContextHolder.setContext(context);
                return recruitingKpiUserExtendRepository.searchApplicationDetailCount(searchDto, teamDTO);
            }, executorService);
        }
        List<? extends RecruitingKpiApplicationBaseDetailVO> voList = dataFuture.join();
        if (withCountFlag) {
            voList.parallelStream().forEach(vo -> {
                if (searchDto.getPermissionRespDTO() != null && Objects.equals(searchDto.getPermissionRespDTO().getTeamIdForPrivateJob(), vo.getPteamId())) {
                    vo.setPrivateJob(true);
                }
            });
        }
        return new PageImpl<>(voList, Pageable.unpaged(), countFuture.join());
    }

    @Override
    public void exportApplicationDetailList(RecruitingKpiApplicationDetailSearchDto searchDto, HttpServletResponse response, String filename) {
        StopWatch stopWatch = new StopWatch("exportApplicationDetailList");
        stopWatch.start("1. search data");
        CompletableFuture<Map<Long, UserBriefDTO>> userMapFuture = getUserMapFuture();
        List<? extends RecruitingKpiApplicationBaseDetailVO> voList = self.searchApplicationDetailPage(searchDto, null, false).getContent();
        stopWatch.stop();
        stopWatch.start("2. 实体转换");
        Map<Long, UserBriefDTO> map = userMapFuture.join();
        Class<?> clazz = APPLICATION_EXCEL_CLASS_MAP.get(searchDto.getReportApplicationStatus());
        List<Object> list = new ArrayList<>();
        List<EnumDictDTO> countryList = companyService.findAllCountry().getBody();
        Map<String, String> countryMap = countryList.stream().collect(Collectors.toMap(EnumDictDTO::getId, EnumDictDTO::getName));

        List<UserBriefDTO> allUserList = userService.getAllBriefUsers().getBody();
        Map<Long, String> userMap = allUserList.stream().collect(Collectors.toMap(UserBriefDTO::getId, UserBriefDTO::getFullName));
        voList.forEach(vo -> {
            try {
                Object o = clazz.newInstance();
                vo.setAm(getUserNameByMap(vo.getAm(), map));
                vo.setRecruiter(getUserNameByMap(vo.getRecruiter(), map));
                vo.setSourcer(getUserNameByMap(vo.getSourcer(), map));
                vo.setAc(getUserNameByMap(vo.getAc(), map));
                vo.setDm(getUserNameByMap(vo.getDm(), map));
                vo.setOwner(getUserNameByMap(vo.getOwner(), map));
                vo.setSalesLeadOwner(getUserNameByMap(vo.getSalesLeadOwner(), map));
                vo.setBdOwner(getUserNameByMap(vo.getBdOwner(), map));
                vo.setLastModifiedBy(getUserNameByMap(vo.getLastModifiedBy(), map));
                if (searchDto.getReportApplicationStatus().equals(ReportApplicationStatus.SUBMIT_TO_JOB) ||
                        searchDto.getReportApplicationStatus().equals(ReportApplicationStatus.SUBMIT_TO_CLIENT)) {
                    if (vo.getStayedOver() != null) {
                        ReflectUtil.setFieldValue(o, "stayedOverStr", vo.getStayedOver() ? "Yes" : "No");
                    }
                }

                if (BooleanUtils.isTrue(vo.getResigned())){
                    vo.setWorkflowStatus(MyCandidateStatusFilter.OFF_BOARDED);
                }

                if (CollUtil.isNotEmpty(vo.getCoAmList())) {
                    List<UserCountryVO> coAmList = new ArrayList<>();
                    vo.getCoAmList().forEach(v -> {
                        if (userMap.containsKey(v.getUserId())) {
                            UserCountryVO countryVO = new UserCountryVO();
                            countryVO.setUserName(userMap.get(v.getUserId()));
                            countryVO.setUserId(v.getUserId());
                            if (countryMap.containsKey(v.getCountryId().toString())) {
                                countryVO.setCountryName(countryMap.get(v.getCountryId().toString()));
                            }
                            coAmList.add(countryVO);
                        }
                    });
                    if (!coAmList.isEmpty()) {
                        List<String> coAmStr = new ArrayList<>();
                        Map<Long, List<UserCountryVO>> userCountryMap = coAmList.stream().collect(Collectors.groupingBy(b -> b.getUserId()));
                        for (Map.Entry<Long, List<UserCountryVO>> entry : userCountryMap.entrySet()) {
                            List<UserCountryVO> userCountryVOList = entry.getValue();
                            List<String> country = new ArrayList<>();
                            userCountryVOList.forEach(v -> {
                                if (country.isEmpty()) {
                                    country.add(v.getCountryName());
                                } else {
                                    country.add("," + v.getCountryName());
                                }
                            });
                            if (country.isEmpty()) {
                                coAmStr.add(userCountryVOList.get(0).getUserName());
                            } else {
                                coAmStr.add(userCountryVOList.get(0).getUserName() + "(" + String.join(StrUtil.COMMA, country) + ")");
                            }
                        }
                        if (!coAmStr.isEmpty()) {
                            vo.setCoAm(String.join(StrUtil.COMMA, coAmStr));
                        }
                    }
                }

                BeanUtil.copyProperties(vo, o);
                if (vo.getSubmitDate() != null) {
                    ReflectUtil.setFieldValue(o, "submitDateFormat",  formatDate(vo.getSubmitDate(), StringUtils.firstNonEmpty(searchDto.getDownloadExcelTimezone(), searchDto.getTimezone()), "yyyy-MM-dd"));
                }
                if (vo.getLastModifiedDate() != null) {
                    ReflectUtil.setFieldValue(o, "lastModifiedDateFormat", formatDate(vo.getLastModifiedDate(), StringUtils.firstNonEmpty(searchDto.getDownloadExcelTimezone(), searchDto.getTimezone()), "yyyy-MM-dd HH:mm:ss"));
                }
                if (vo.getConfidentialTalentViewAble() != null && !vo.getConfidentialTalentViewAble()) {
                    ExcelUtil.maskConfidentialTalentData(o);
                }
                list.add(o);
            } catch (Exception e) {
                log.info("exportApplicationDetailList error {} ", ExceptionUtil.getAllExceptionMsg(e));
            }
        });
        stopWatch.stop();
        stopWatch.start("3. 输出 excel");
        ExcelUtil.downloadExcel(response, ExcelUtil.getTableHeaders(clazz, new ArrayList<>()), ExcelUtil.convertToMap(list, clazz, new ArrayList<>()), "",  StringUtils.isEmpty(filename) ? "applicationDetail.xlsx" : filename, false);
        stopWatch.stop();
        log.info(" exportApplicationDetailList by user time = {} ms= \n {}", stopWatch.getTotalTimeMillis(), stopWatch.prettyPrint());
    }

    @ProcessConfidentialTalent
    @Override
    public Page<RecruitingKpiApplicationNoteDetailVO> searchApplicationNoteDetailPage(RecruitingKpiApplicationNoteDetailSearchDto searchDto, Pageable pageable, boolean withCountFlag) {
        TeamDataPermissionRespDTO teamDTO = getPermissionDTOAndSetCommonParam(searchDto);
        SecurityContext context = SecurityContextHolder.getContext();
        CompletableFuture<List<RecruitingKpiApplicationNoteDetailVO>> dataFuture = CompletableFuture.supplyAsync(() -> {
            SecurityContextHolder.setContext(context);
            return recruitingKpiUserExtendRepository.searchApplicationNoteDetailList(searchDto, pageable, teamDTO);
        }, executorService);
        CompletableFuture<Long> countFuture = CompletableFuture.supplyAsync(() -> 0L);
        if (withCountFlag) {
            countFuture = CompletableFuture.supplyAsync(() -> {
                SecurityContextHolder.setContext(context);
                return recruitingKpiUserExtendRepository.searchApplicationNoteDetailCount(searchDto, teamDTO);
            }, executorService);
        }
        List<RecruitingKpiApplicationNoteDetailVO> voList = dataFuture.join();
        if (withCountFlag) {
            voList.parallelStream().forEach(vo -> {
                if (searchDto.getPermissionRespDTO() != null && Objects.equals(searchDto.getPermissionRespDTO().getTeamIdForPrivateJob(), vo.getPteamId())) {
                    vo.setPrivateJob(true);
                }
            });
        }
        return new PageImpl<>(voList, Pageable.unpaged(), countFuture.join());
    }

    @Override
    public void exportApplicationNoteDetailList(RecruitingKpiApplicationNoteDetailSearchDto searchDto, HttpServletResponse response) {
        StopWatch stopWatch = new StopWatch("exportApplicationNoteDetailList");
        stopWatch.start("1. search data");
        CompletableFuture<Map<Long, UserBriefDTO>> userMapFuture = getUserMapFuture();
        List<RecruitingKpiApplicationNoteDetailVO> voList = self.searchApplicationNoteDetailPage(searchDto, null, false).getContent();
        stopWatch.stop();
        stopWatch.start("2. 实体转换");
        Map<Long, UserBriefDTO> userMap = userMapFuture.join();
        List<RecruitingKpiApplicationNoteDetailExcelENVO> list = new ArrayList<>();
        voList.forEach(vo -> {
            try {
                RecruitingKpiApplicationNoteDetailExcelENVO detailVo = new RecruitingKpiApplicationNoteDetailExcelENVO();
                BeanUtil.copyProperties(vo, detailVo, true);
                setCreatedAndLastModified(vo.getCreatedBy(), vo.getCreatedDate(), vo.getLastModifiedBy(), vo.getLastModifiedDate(), searchDto.getTimezone(), detailVo, userMap);
                if (vo.getConfidentialTalentViewAble() != null && !vo.getConfidentialTalentViewAble()) {
                    ExcelUtil.maskConfidentialTalentData(detailVo);
                }
                list.add(detailVo);
            } catch (Exception e) {
                log.error("exportTalentDetailList error {} ", ExceptionUtil.getAllExceptionMsg(e));
            }
        });
        stopWatch.stop();
        stopWatch.start("3. 输出 excel");
        ExcelUtil.downloadExcel(response, RecruitingKpiApplicationNoteDetailExcelENVO.class, list, "",  "applicationNoteDetail.xlsx", false);
        stopWatch.stop();
        log.info(" exportApplicationNoteDetailList by user time = {} ms= \n {}", stopWatch.getTotalTimeMillis(), stopWatch.prettyPrint());
    }

    @ProcessConfidentialTalent
    @Override
    public Page<RecruitingKpiApnProNoteDetailVO> searchApnProNoteDetailPage(RecruitingKpiApnProNoteDetailSearchDto searchDto, Pageable pageable, boolean withCountFlag) {
        TeamDataPermissionRespDTO teamDTO = getPermissionDTOAndSetCommonParam(searchDto);
        SecurityContext context = SecurityContextHolder.getContext();
        CompletableFuture<List<RecruitingKpiApnProNoteDetailVO>> dataFuture = CompletableFuture.supplyAsync(() -> {
            SecurityContextHolder.setContext(context);
            return recruitingKpiUserExtendRepository.searchApnProNoteDetailList(searchDto, pageable, teamDTO);
        }, executorService);

        CompletableFuture<Long> countFuture = CompletableFuture.supplyAsync(() -> 0L);
        if (withCountFlag) {
            countFuture = CompletableFuture.supplyAsync(() -> {
                SecurityContextHolder.setContext(context);
                return recruitingKpiUserExtendRepository.searchApnProNoteDetailCount(searchDto, teamDTO);
            }, executorService);
        }
        List<RecruitingKpiApnProNoteDetailVO> voList = dataFuture.join();
        return new PageImpl<>(voList, Pageable.unpaged(), countFuture.join());
    }

    @Override
    public void exportApnProNoteDetailList(RecruitingKpiApnProNoteDetailSearchDto searchDto, HttpServletResponse response) {
        StopWatch stopWatch = new StopWatch("exportApplicationNoteDetailList");
        stopWatch.start("1. search data");
        CompletableFuture<Map<Long, UserBriefDTO>> userMapFuture = getUserMapFuture();
        Map<Integer, EnumMotivation> enumMotivationMap = enumMotivationService.findAllEnumMotivation().stream().collect(Collectors.toMap(EnumMotivation::getId, a -> a));
        List<RecruitingKpiApnProNoteDetailVO> voList = self.searchApnProNoteDetailPage(searchDto, null, false).getContent();
        stopWatch.stop();
        stopWatch.start("2. 实体转换");
        Map<Long, UserBriefDTO> userMap = userMapFuture.join();
        List<RecruitingKpiApnProNoteDetailExcelENVO> list = new ArrayList<>();
        voList.forEach(vo -> {
            try {
                RecruitingKpiApnProNoteDetailExcelENVO detailVo = new RecruitingKpiApnProNoteDetailExcelENVO();
                BeanUtil.copyProperties(vo, detailVo, true);
                setCreatedAndLastModified(vo.getCreatedBy(), vo.getCreatedDate(), vo.getLastModifiedBy(), vo.getLastModifiedDate(), searchDto.getTimezone(), detailVo, userMap);
                setJobSearchStatusDisplay(enumMotivationMap, vo.getJobSearchStatus(), detailVo, searchDto.getType());
                if (vo.getConfidentialTalentViewAble() != null && !vo.getConfidentialTalentViewAble()) {
                    ExcelUtil.maskConfidentialTalentData(detailVo);
                }
                list.add(detailVo);
            } catch (Exception e) {
                log.error("exportTalentDetailList error {} ", ExceptionUtil.getAllExceptionMsg(e));
            }
        });
        stopWatch.stop();
        stopWatch.start("3. 输出 excel");
        ExcelUtil.downloadExcel(response, RecruitingKpiApnProNoteDetailExcelENVO.class, list, "",  "applicationNoteDetail.xlsx", false);
        stopWatch.stop();
        log.info(" exportApplicationNoteDetailList by user time = {} ms= \n {}", stopWatch.getTotalTimeMillis(), stopWatch.prettyPrint());
    }

    @ProcessConfidentialTalent
    @Override
    public Page<RecruitingKpiTalentNoteDetailVO> searchTalentNoteDetailPage(RecruitingKpiTalentNoteDetailSearchDto searchDto, Pageable pageable, boolean withCountFlag) {
        TeamDataPermissionRespDTO teamDTO = getPermissionDTOAndSetCommonParam(searchDto);
        SecurityContext context = SecurityContextHolder.getContext();
        CompletableFuture<List<RecruitingKpiTalentNoteDetailVO>> dataFuture = CompletableFuture.supplyAsync(() -> {
            SecurityContextHolder.setContext(context);
            return recruitingKpiUserExtendRepository.searchTalentNoteDetailList(searchDto, pageable, teamDTO);
        }, executorService);
        CompletableFuture<Long> countFuture = CompletableFuture.supplyAsync(() -> 0L);
        if (withCountFlag) {
            countFuture = CompletableFuture.supplyAsync(() -> {
                SecurityContextHolder.setContext(context);
                return recruitingKpiUserExtendRepository.searchTalentNoteDetailCount(searchDto, teamDTO);
            }, executorService);
        }
        List<RecruitingKpiTalentNoteDetailVO> voList = dataFuture.join();
        return new PageImpl<>(voList, Pageable.unpaged(), countFuture.join());
    }

    @Override
    public void exportTalentNoteDetailList(RecruitingKpiTalentNoteDetailSearchDto searchDto, HttpServletResponse response, String fileName, boolean exportAttendees) {
        StopWatch stopWatch = new StopWatch("exportTalentNoteDetailList");
        stopWatch.start("1. search data");
        CompletableFuture<Map<Long, UserBriefDTO>> userMapFuture = getUserMapFuture();
        Map<Integer, EnumMotivation> enumMotivationMap = enumMotivationService.findAllEnumMotivation().stream().collect(Collectors.toMap(EnumMotivation::getId, a -> a));
        List<RecruitingKpiTalentNoteDetailVO> voList = self.searchTalentNoteDetailPage(searchDto, null, false).getContent();
        stopWatch.stop();
        stopWatch.start("2. 实体转换");
        Map<Long, UserBriefDTO> userMap = userMapFuture.join();
        List<RecruitingKpiTalentNoteDetailExcelENVO> list = new ArrayList<>();
        voList.forEach(vo -> {
            try {
                RecruitingKpiTalentNoteDetailExcelENVO detailVo = new RecruitingKpiTalentNoteDetailExcelENVO();
                BeanUtil.copyProperties(vo, detailVo, true);
                setCreatedAndLastModified(vo.getCreatedBy(), vo.getCreatedDate(), vo.getLastModifiedBy(), vo.getLastModifiedDate(), StringUtils.firstNonBlank(searchDto.getDownloadExcelTimezone(), searchDto.getTimezone()), detailVo, userMap);
                setJobSearchStatusDisplay(enumMotivationMap, vo.getJobSearchStatus(), detailVo, searchDto.getType());
                String attendees = vo.getAttendeeIds().stream().map(userId -> userMap.getOrDefault((userId), new UserBriefDTO()).getFullName())
                        .filter(StrUtil::isNotBlank)
                        .collect(Collectors.joining(","));
                detailVo.setNoteType(vo.getNoteType() == null ? "" : vo.getNoteType().getEnDisplay());
                detailVo.setAttendees(attendees);
                if (vo.getConfidentialTalentViewAble() != null && !vo.getConfidentialTalentViewAble()) {
                    ExcelUtil.maskConfidentialTalentData(detailVo);
                }
                list.add(detailVo);
            } catch (Exception e) {
                log.error("exportTalentDetailList error {} ", ExceptionUtil.getAllExceptionMsg(e));
            }
        });
        stopWatch.stop();
        stopWatch.start("3. 输出 excel");
        List<String> removeHeaderList = new ArrayList<>();
        if (TalentNoteType.OTHERS != searchDto.getTalentNoteType()) {
            removeHeaderList.add("additionalInfo");
        }
        if (!exportAttendees) {
            removeHeaderList.add("attendees");
            removeHeaderList.add("noteType");
        } else {
            removeHeaderList.add("jobSearchStatusDisplay");
            removeHeaderList.add("lastModifiedDateFormat");
        }
        ExcelUtil.downloadExcel(response, ExcelUtil.getTableHeaders(RecruitingKpiTalentNoteDetailExcelENVO.class, removeHeaderList), ExcelUtil.convertToMap(list, RecruitingKpiTalentNoteDetailExcelENVO.class, removeHeaderList), "",  StringUtils.isEmpty(fileName) ? "talentNoteDetail.xlsx" : fileName, false);
        stopWatch.stop();
        log.info(" exportApplicationNoteDetailList by user time = {} ms= \n {}", stopWatch.getTotalTimeMillis(), stopWatch.prettyPrint());
    }

    @Override
    public Page<RecruitingKpiUpgradeToClientDetailVO> searchUpgradeToClientDetailList(RecruitingKpiUserCompanyDetailSearchDto searchDto, Pageable pageable) {
        if (Stream.of(RecruitingKpiGroupByFieldType.TEAM, RecruitingKpiGroupByFieldType.USER)
                .anyMatch(searchDto.getGroupByFieldList()::contains)) {
            if (null != searchDto.getUserId()) {
                searchDto.setUserIdList(Arrays.asList(searchDto.getUserId()));
                searchDto.setTeamIdList(new ArrayList<>());
            } else {
                List<CrmUserIdAndTeamIdVO> voList = recruitingKpiCompanyRepository.findCrmUserIdByTeamId(Arrays.asList(searchDto.getTeamId()), searchDto.getUser());
                if (!voList.isEmpty()) {
                    List<Long> userIdList = voList.stream().map(CrmUserIdAndTeamIdVO::getUserId).collect(Collectors.toList());
                    if (CollUtil.isEmpty(searchDto.getUserIdList())) {
                        searchDto.setUserIdList(new ArrayList<>(userIdList));
                        searchDto.setTeamIdList(new ArrayList<>());
                    } else {
                        // 团队下的用户和搜索条件的用户做交集
                        Collection<Long> intersection = CollUtil.intersection(userIdList, searchDto.getUserIdList());
                        searchDto.setUserIdList(new ArrayList<>(intersection));
                        searchDto.setTeamIdList(new ArrayList<>());
                    }
                } else {
                    new PageImpl<>(new ArrayList<>(), Pageable.unpaged(), 0);
                }
            }
        }
        return recruitingKpiUserExtendRepository.searchUpgradeToClientDetailList(searchDto,pageable);
    }

    @Override
    public void exportUpgradeToClientDetailList(RecruitingKpiUserCompanyDetailSearchDto searchDto, HttpServletResponse response) {
        Pageable pageable = PageRequest.of(1,100000);
        Page<RecruitingKpiUpgradeToClientDetailVO> result = searchUpgradeToClientDetailList(searchDto,pageable);
        StopWatch stopWatch = new StopWatch("exportUpgradeToClientDetailList");
        stopWatch.start("1. search data");
        List<RecruitingKpiUpgradeToClientDetailVO> voList = result.getContent();
        stopWatch.stop();
        stopWatch.start("2. 实体转换");
        List<RecruitingKpiUpgradeToClientDetailExcelVO> list = new ArrayList<>();
        List<EnumDictDTO> countryList = companyService.findAllCountry().getBody();
        Map<String, String> countryMap = countryList.stream().collect(Collectors.toMap(EnumDictDTO::getId, EnumDictDTO::getName));
        voList.forEach(vo -> {
            try {
                RecruitingKpiUpgradeToClientDetailExcelVO detailVo = new RecruitingKpiUpgradeToClientDetailExcelVO();
                detailVo.setCompanyName(vo.getCompanyName());
                detailVo.setBdOwnerName(vo.getBdOwnerName());
                detailVo.setAmName(vo.getAmName());
                if (CollUtil.isNotEmpty(vo.getCoAmList())) {
                    List<String> coAmStr = new ArrayList<>();
                    Map<String, List<UserCountryVO>> userCountryMap = vo.getCoAmList().stream().collect(Collectors.groupingBy(b -> b.getUserName()));
                    for (Map.Entry<String, List<UserCountryVO>> entry : userCountryMap.entrySet()) {
                        List<UserCountryVO> userCountryVOList = entry.getValue();
                        List<String> country = new ArrayList<>();
                        userCountryVOList.forEach(v -> {
                            if (country.isEmpty()) {
                                if (countryMap.containsKey(v.getCountryId().toString())) {
                                    country.add(countryMap.get(v.getCountryId().toString()));
                                }
                            } else {
                                if (countryMap.containsKey(v.getCountryId().toString())) {
                                    country.add("," + countryMap.get(v.getCountryId().toString()));
                                }
                            }
                        });
                        if (country.isEmpty()) {
                            coAmStr.add(userCountryVOList.get(0).getUserName());
                        } else {
                            coAmStr.add(userCountryVOList.get(0).getUserName() + "(" + String.join(StrUtil.COMMA, country) + ")");
                        }
                    }
                    if (!coAmStr.isEmpty()) {
                        detailVo.setCoAmName(String.join(StrUtil.COMMA, coAmStr));
                    }
                }
                detailVo.setOpenJobCount(vo.getOpenJobCount());
                detailVo.setSalesLeadName(vo.getSalesLeadName());
                detailVo.setCreatedDate(DateUtil.fromInstantToDate(vo.getCreatedDate()));
                detailVo.setRequestDate(DateUtil.fromInstantToDate(vo.getRequestDate()));
                list.add(detailVo);
            } catch (Exception e) {
                log.error("exportTalentDetailList error {} ", ExceptionUtil.getAllExceptionMsg(e));
            }
        });
        stopWatch.stop();
        stopWatch.start("3. 输出 excel");
        ExcelUtil.downloadExcel(response, ExcelUtil.getTableHeaders(RecruitingKpiUpgradeToClientDetailExcelVO.class,new ArrayList<>()), ExcelUtil.convertToMap(list, RecruitingKpiUpgradeToClientDetailExcelVO.class, new ArrayList<>()), "",  "upgradeToClientDetail.xlsx", false);
        stopWatch.stop();
        log.info(" exportTalentDetailList by user time = {} ms= \n {}", stopWatch.getTotalTimeMillis(), stopWatch.prettyPrint());
    }

    private void setUserId(RecruitingKpiDetailBaseDto searchDto, Pageable pageable) {

        if (Stream.of(RecruitingKpiGroupByFieldType.TEAM, RecruitingKpiGroupByFieldType.USER)
                .anyMatch(searchDto.getGroupByFieldList()::contains)) {
            if (null != searchDto.getUserId()) {
                if (CollUtil.isEmpty(searchDto.getUserIdList())) {
                    searchDto.setUserIdList(List.of(searchDto.getUserId()));
                } else {
                    // 两个集合做交集
                    Collection<Long> union = CollUtil.intersection(searchDto.getUserIdList(), List.of(searchDto.getUserId()));
                    if (union.isEmpty()) {
                        new PageImpl<>(new ArrayList<>(), pageable, 0);
                    }
                    searchDto.setUserIdList(new ArrayList<>(union));
                }
            } else {
                List<CrmUserIdAndTeamIdVO> voList = recruitingKpiCompanyRepository.findCrmUserIdByTeamId(Collections.singletonList(searchDto.getTeamId()), searchDto.getUser());
                if (voList.isEmpty()) {
                    new PageImpl<>(new ArrayList<>(), Pageable.unpaged(), 0);
                }

                List<Long> userIdList = voList.stream().map(CrmUserIdAndTeamIdVO::getUserId).collect(Collectors.toList());
                if (CollUtil.isEmpty(searchDto.getUserIdList())) {
                    searchDto.setUserIdList(userIdList);
                } else {
                    Collection<Long> union = CollUtil.intersection(userIdList, searchDto.getUserIdList());
                    if (union.isEmpty()) {
                        new PageImpl<>(new ArrayList<>(), pageable, 0);
                    }
                    searchDto.setUserIdList(new ArrayList<>(union));
                }
            }
        } else {
            List<Long> userIds = new ArrayList<>();
            if (CollUtil.isNotEmpty(searchDto.getUserIdList())) {
                List<CrmUserIdAndTeamIdVO> voList = recruitingKpiCompanyRepository.findCrmUserIdByUserId(searchDto.getUserIdList(), searchDto.getUser());
                if (!voList.isEmpty()) {
                    List<Long> userIdList = voList.stream().map(CrmUserIdAndTeamIdVO::getCrmUserId).collect(Collectors.toList());
                    userIds.addAll(userIdList);
                } else {
                    new PageImpl<>(new ArrayList<>(), Pageable.unpaged(), 0);
                }
            }
            if (CollUtil.isNotEmpty(searchDto.getTeamIdList())) {
                List<CrmUserIdAndTeamIdVO> voList = recruitingKpiCompanyRepository.findCrmUserIdByTeamId(searchDto.getTeamIdList(), searchDto.getUser());
                if (!voList.isEmpty()) {
                    List<Long> userIdList = voList.stream().map(CrmUserIdAndTeamIdVO::getCrmUserId).collect(Collectors.toList());
                    userIds.addAll(userIdList);
                } else {
                    new PageImpl<>(new ArrayList<>(), Pageable.unpaged(), 0);
                }
            }
            List<Long> finalUserIds = userService.getUserIdFilterByTeamCategory(userIds).getBody();
            searchDto.setUserIdList(finalUserIds);
        }
    }

    @Override
    public Page<BDReportKpiUserCompanyDetailVO> searchCreateCompanyDetailList(RecruitingKpiUserCompanyDetailSearchDto searchDto, Pageable pageable) {

        setUserId(searchDto,pageable);
        //String url = "http://localhost:9008/report/api/v1/bd-report/kpi-user/company-info/list?page=" + pageable.getPageNumber() + "&size=" + pageable.getPageSize();
        String url =  crmUrl + "/report/api/v1/bd-report/kpi-user/company-info/list?page=" + pageable.getPageNumber() + "&size=" + pageable.getPageSize();
        log.info("[apn searchCreateCompanyDetailList {}] url = {}", SecurityUtils.getUserId(), url);
        Headers headers = Headers.of(Map.of(HttpHeaders.AUTHORIZATION, "Bearer " + SecurityUtils.getCurrentUserToken()));
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("userIdList", searchDto.getUserIdList());
        jsonObject.put("teamIdList", searchDto.getTeamIdList());
        jsonObject.put("fromDate", searchDto.getStartDate());
        jsonObject.put("toDate", searchDto.getEndDate());
        jsonObject.put("timezone", searchDto.getTimezone());
        jsonObject.put("tenantId", SecurityUtils.getTenantId());
        if (ObjectUtil.isNotEmpty(searchDto.getSort())) {
            jsonObject.put("descFiled", searchDto.getSort().getDirection());
            jsonObject.put("propertyFiled", searchDto.getSort().getProperty());
        }
        if (Stream.of(RecruitingKpiGroupByFieldType.TEAM, RecruitingKpiGroupByFieldType.USER)
                .anyMatch(searchDto.getGroupByFieldList()::contains)) {
            jsonObject.put("containsUser", "1");
        } else {
            jsonObject.put("containsUser", "");
        }
        if (null != searchDto.getDetail() && null != searchDto.getDetail().getCompanyId()) {
            jsonObject.put("companyId", searchDto.getDetail().getCompanyId());
        }
        if (null != searchDto.getDetail() && null != searchDto.getDetail().getCompanyName()) {
            jsonObject.put("companyName", searchDto.getDetail().getCompanyName());
        }
        HttpResponse response;
        try {
            response = httpService.post(url, headers, jsonObject.toString());
        } catch (IOException e) {
            log.error("[apn searchCreateCompanyDetailList {}] search is error , message = {}", searchDto.getSearchUserId(), ExceptionUtil.getAllExceptionMsg(e));
            throw new RuntimeException(e);
        }
        if (Objects.equals(response.getCode(), 200)) {
            BDReportKpiUserCompanyDetailPageVO clientVOS = JSONUtil.toBean(response.getBody(), BDReportKpiUserCompanyDetailPageVO.class);
            if (null != clientVOS) {
                clientVOS.getDetailVOList().forEach(x -> {
                    if (StringUtils.isNotBlank(x.getCoAmName())) {
                        List<UserCountryVO> voList = new ArrayList<>();
                        Arrays.stream(x.getCoAmName().split(",")).forEach(v -> {
                            String[] amCountry = v.split("-");
                            UserCountryVO vo = new UserCountryVO();
                            vo.setUserName(amCountry[0]);
                            vo.setCountryId(amCountry[1]);
                            voList.add(vo);
                        });
                        x.setCoAmList(voList);
                    }
                });
                return new PageImpl<>(clientVOS.getDetailVOList(), Pageable.unpaged(), clientVOS.getTotal());
            }
        }

        return new PageImpl<>(new ArrayList<>(),Pageable.unpaged(),0);
    }

    @Override
    public void exportCreateCompanyDetailList(RecruitingKpiUserCompanyDetailSearchDto searchDto, HttpServletResponse response) {
        Pageable pageable = PageRequest.of(1,100000);
        Page<BDReportKpiUserCompanyDetailVO> result = searchCreateCompanyDetailList(searchDto,pageable);
        StopWatch stopWatch = new StopWatch("exportCreateCompanyDetailList");
        stopWatch.start("1. search data");
        List<BDReportKpiUserCompanyDetailVO> voList = result.getContent();
        stopWatch.stop();
        stopWatch.start("2. 实体转换");
        List<BDReportKpiUserCompanyDetailExcelVO> list = new ArrayList<>();
        List<EnumDictDTO> countryList = companyService.findAllCountry().getBody();
        Map<String, String> countryMap = countryList.stream().collect(Collectors.toMap(EnumDictDTO::getId, EnumDictDTO::getName));
        voList.forEach(vo -> {
            try {
                BDReportKpiUserCompanyDetailExcelVO detailVo = new BDReportKpiUserCompanyDetailExcelVO();
                detailVo.setCompanyName(vo.getCompanyName());
                detailVo.setBdOwnerName(vo.getBdOwnerName());
                detailVo.setAmName(vo.getAmName());
                if (CollUtil.isNotEmpty(vo.getCoAmList())) {
                    List<String> coAmStr = new ArrayList<>();
                    Map<String, List<UserCountryVO>> userCountryMap = vo.getCoAmList().stream().collect(Collectors.groupingBy(b -> b.getUserName()));
                    for (Map.Entry<String, List<UserCountryVO>> entry : userCountryMap.entrySet()) {
                        List<UserCountryVO> userCountryVOList = entry.getValue();
                        List<String> country = new ArrayList<>();
                        userCountryVOList.forEach(v -> {
                            if (country.isEmpty()) {
                                if (countryMap.containsKey(v.getCountryId().toString())) {
                                    country.add(countryMap.get(v.getCountryId().toString()));
                                }
                            } else {
                                if (countryMap.containsKey(v.getCountryId().toString())) {
                                    country.add("," + countryMap.get(v.getCountryId().toString()));
                                }
                            }
                        });
                        if (country.isEmpty()) {
                            coAmStr.add(userCountryVOList.get(0).getUserName());
                        } else {
                            coAmStr.add(userCountryVOList.get(0).getUserName() + "(" + String.join(StrUtil.COMMA, country) + ")");
                        }
                    }
                    if (!coAmStr.isEmpty()) {
                        detailVo.setCoAmName(String.join(StrUtil.COMMA, coAmStr));
                    }
                }
                detailVo.setSalesLeadName(vo.getSalesLeadName());
                detailVo.setCreatedDateStr(DateUtil.formatDate(vo.getCreatedDate()));
                list.add(detailVo);
            } catch (Exception e) {
                log.error("exportCreateCompanyDetailList error {} ", ExceptionUtil.getAllExceptionMsg(e));
            }
        });
        stopWatch.stop();
        stopWatch.start("3. 输出 excel");
        ExcelUtil.downloadExcel(response, ExcelUtil.getTableHeaders(BDReportKpiUserCompanyDetailExcelVO.class,new ArrayList<>()),
                ExcelUtil.convertToMap(list, BDReportKpiUserCompanyDetailExcelVO.class, new ArrayList<>()),
                "",  "createCompanyDetail.xlsx", false);
        stopWatch.stop();
        log.info(" exportCreateCompanyDetailList by user time = {} ms= \n {}", stopWatch.getTotalTimeMillis(), stopWatch.prettyPrint());
    }
}
