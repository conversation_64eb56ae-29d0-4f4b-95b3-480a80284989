package com.altomni.apn.canal.disruptor.handler;

import com.alibaba.nacos.common.utils.ExceptionUtil;
import com.alibaba.otter.canal.protocol.CanalEntry;
import com.altomni.apn.canal.config.RabbitMqConstant;
import com.altomni.apn.canal.disruptor.CanalEvent;
import com.altomni.apn.canal.disruptor.EventContent;
import com.altomni.apn.canal.service.sync.talent.SyncTalentService;
import com.altomni.apn.common.domain.enumeration.canal.FailReasonEnum;
import com.altomni.apn.common.domain.enumeration.canal.SyncIdTypeEnum;
import com.altomni.apn.common.service.canal.CanalService;
import com.google.common.collect.Iterables;
import com.lmax.disruptor.EventHandler;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.altomni.apn.canal.service.canal.CanalClient.*;

@Slf4j
@Component
public class TalentSyncHandler implements EventHandler<CanalEvent> {

    private final CanalService canalService;
    private final SyncTalentService syncTalentService;
    private final List<String> TALENT_LOCATION_FILL_BACK = List.of("official_county", "official_country", "official_city", "official_province");

    public TalentSyncHandler(CanalService canalService, SyncTalentService syncTalentService) {
        this.canalService = canalService;
        this.syncTalentService = syncTalentService;
    }

    @Override
    public void onEvent(CanalEvent event, long sequence, boolean endOfBatch) throws Exception {
        log.info("-----------------------------> TalentSyncHandler onEvent start <-");
        List<EventContent> eventContents = event.getEventContents();
        Set<Long> talentChangeIds = eventContents.stream()
                .filter(eventContent -> TALENT.equals(eventContent.tableName()) || TALENT_RELATED_TABLES.contains(eventContent.tableName()))
                .flatMap(eventContent -> {
                    String tableName = eventContent.tableName();
                    CanalEntry.RowChange rowChange = eventContent.rowChange();
                    CanalEntry.EventType eventType = rowChange.getEventType();
                    log.info("[canal] syncTable, table {} changed, event type {}", tableName, eventType);

                    List<CanalEntry.RowData> rowDataList = rowChange.getRowDatasList();

                    return switch (eventType) {
                        case INSERT -> rowDataList.stream()
                                .map(rowData -> changeRows(rowData.getAfterColumnsList(), tableName, false))
                                .filter(Optional::isPresent)
                                .map(Optional::get);
                        case UPDATE -> rowDataList.stream()
                                .map(rowData -> changeRows(rowData.getAfterColumnsList(), tableName, true))
                                .filter(Optional::isPresent)
                                .map(Optional::get);
                        case DELETE -> rowDataList.stream()
                                .map(rowData -> rowDeleted(rowData.getBeforeColumnsList(), tableName))
                                .filter(Optional::isPresent)
                                .map(Optional::get);
                        default -> Stream.of();
                    };
                })
                .collect(Collectors.toSet());

        if (talentChangeIds.isEmpty()) {
            return;
        }
        syncTalentsToEs(talentChangeIds);
    }

    private Optional<Long> changeRows(List<CanalEntry.Column> columns, String tableName, boolean update) {
        if (TALENT.equals(tableName)) {
            return talentChange(columns, tableName, update);
        }
        if (TALENT_RELATED_TABLES.contains(tableName)) {
            return talentRelatedChange(columns, tableName);
        }
        return Optional.empty();
    }

    private void syncTalentsToEs(Set<Long> pendingTalentsToSync) {
        log.info("startSyncTalentsToEs {}", pendingTalentsToSync);
        try {
            for (List<Long> partition : Iterables.partition(pendingTalentsToSync, 50)) {
                syncTalentService.synchronizeTalents(partition, RabbitMqConstant.MESSAGE_PRIORITY_CRITICAL, 0);
            }
        } catch (Exception e) {
            log.error("[Canal] sync_talent_to_es error, talents {}, error: {}", pendingTalentsToSync, ExceptionUtil.getStackTrace(e));
            canalService.insertAll(pendingTalentsToSync, SyncIdTypeEnum.TALENT, FailReasonEnum.ERROR, e.getMessage(), RabbitMqConstant.MESSAGE_PRIORITY_CRITICAL);
        }
    }

    private Optional<Long> talentChange(List<CanalEntry.Column> columns, String tableName, boolean update) {
        String talentId = null;
        for (CanalEntry.Column column : columns) {
            String name = column.getName();
            boolean columnUpdated = column.getUpdated();
            if (columnUpdated){
                log.info("talentChange: table: {}, column: {}, value: {}", tableName, name, column.getValue());
            }
            // for back filling
            if (update && name.equals(LAST_SYNC_TIME) && columnUpdated) {
                String value = column.getValue();
                log.info("LAST_SYNC_TIME : {}", value);
                return Optional.empty();
            }
            if (name.equals(ID)) {
                talentId = column.getValue();
            }
        }
        log.info("canal received changed talentId: {} from table: {}", talentId, tableName);
        return Optional.ofNullable(talentId).filter(StringUtils::isNotEmpty).map(Long::parseLong);
    }

    private Optional<Long> talentRelatedChange(List<CanalEntry.Column> columns, String tableName) {
        String talentId = null;
        //only update ai source field, not sync job
        boolean onlyUpdateAiSource = columns.stream().filter(CanalEntry.Column::getUpdated)
                .allMatch(column -> "talent_recruitment_process".equals(tableName) && "ai_score".equals(column.getName()));
        if (onlyUpdateAiSource) {
            return Optional.empty();
        }
        //talent_current_location回填official_county/official_country/official_city/official_province 时不用重新同步
        boolean onlyUpdateOfficialLocation = columns.stream().filter(CanalEntry.Column::getUpdated)
                .allMatch(column -> "talent_current_location".equals(tableName) && TALENT_LOCATION_FILL_BACK.contains(column.getName()) && column.getUpdated());
        if (onlyUpdateOfficialLocation) {
            return Optional.empty();
        }
        for (CanalEntry.Column column : columns) {
            String name = column.getName();
            String value = column.getValue();
            boolean columnUpdated = column.getUpdated();
            if (columnUpdated){
                log.info("talentChange: table: {}, column: {}, value: {}", tableName, name, value);
            }
            if (name.equals(TALENT_ID)) {
                if (StringUtils.isNotEmpty(value)) {
                    talentId = value;
                }
            } else if (LAST_MODIFIED_DATE.equals(name)) {
                log.info("table name : {}, LAST_MODIFIED_DATE : {}", tableName, value);
            }
            log.debug("talentRelatedChange: table: {}, column: {}, value: {}, updated: {}", tableName, name, value, columnUpdated);
        }
        log.info("[Canal] received changed talentId: {} from table: {}", talentId, tableName);
        return Optional.ofNullable(talentId).filter(StringUtils::isNotEmpty).map(Long::parseLong);
    }

    private Optional<Long> rowDeleted(List<CanalEntry.Column> columns, String tableName) {
        if (!TALENT_RELATED_TABLES.contains(tableName)) {
            return Optional.empty();
        }
        return talentRelatedChange(columns, tableName);
    }
}
