package com.altomni.apn.application.repository.dashboard;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.ReflectUtil;
import com.alibaba.fastjson.JSON;
import com.altomni.apn.application.web.rest.vm.InvoiceXxlJobVO;
import com.altomni.apn.application.web.rest.vm.KpiTopVo;
import com.altomni.apn.application.web.rest.vm.TeamPerformanceVO;
import com.altomni.apn.common.domain.dict.CurrencyRateDay;
import com.altomni.apn.common.domain.dict.EnumCurrency;
import com.altomni.apn.common.domain.enumeration.application.NodeStatus;
import com.altomni.apn.common.domain.enumeration.application.NodeType;
import com.altomni.apn.common.domain.enumeration.job.JobStatus;
import com.altomni.apn.common.domain.enumeration.job.JobType;
import com.altomni.apn.common.domain.enumeration.jobdiva.GroupInvoiceStatus;
import com.altomni.apn.common.repository.enums.CurrencyRateDayRepository;
import com.altomni.apn.common.service.enums.EnumCurrencyService;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.finance.domain.enumeration.invoice.InvoiceStatus;
import com.altomni.apn.finance.domain.enumeration.invoice.InvoiceType;
import com.altomni.apn.job.domain.project.JobProject;
import com.altomni.apn.job.web.rest.vm.MyJobVo;
import lombok.extern.slf4j.Slf4j;
import org.hibernate.SQLQuery;
import org.hibernate.transform.Transformers;
import org.springframework.stereotype.Repository;
import org.springframework.util.StopWatch;

import javax.annotation.Resource;
import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;
import javax.persistence.Query;
import java.lang.reflect.Method;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.time.Instant;
import java.util.*;

@Slf4j
@Repository
public class DashboardRepository {

    @PersistenceContext
    private EntityManager entityManager;

    @Resource
    private EnumCurrencyService enumCurrencyService;

    @Resource
    private CurrencyRateDayRepository currencyRateDayRepository;

    public LinkedHashMap<String, Integer> myApplicationCandidates(Instant startTime, Instant endTime, List<Long> userIdList) {
        StopWatch stopWatch = new StopWatch("myApplicationCandidates");
        stopWatch.start("1. search nodeTypeList");
        LinkedHashMap<String, Integer> result = new LinkedHashMap<>(16);
        String nodeTypeSql = "SELECT distinct rpn.node_type " +
                " FROM recruitment_process rp " +
                " INNER JOIN recruitment_process_node rpn ON rp.id = rpn.recruitment_process_id " +
                " where rp.tenant_id = :tenantId";
        List<Integer> nodeTypeList = entityManager.createNativeQuery(nodeTypeSql)
                .setParameter("tenantId", SecurityUtils.getTenantId()).getResultList();
        stopWatch.stop();
        if (CollUtil.isEmpty(nodeTypeList)) {
            log.info("myApplicationCandidates time = {}ms", stopWatch.getTotalTimeMillis());
            return result;
        }
        stopWatch.start("2. nodeType count");
        String sql = " SELECT trpn.node_status, trpn.node_type, count(distinct trpn.talent_recruitment_process_id), count(distinct resign.talent_recruitment_process_id) " +
                " FROM talent_recruitment_process trp " +
                " INNER JOIN talent_recruitment_process_kpi_user trpku ON trpku.talent_recruitment_process_id = trp.id " +
                " INNER JOIN talent_recruitment_process_node trpn ON trpn.talent_recruitment_process_id = trp.id " +
                " LEFT JOIN talent_recruitment_process_resignation resign ON resign.talent_recruitment_process_id = trp.id " +
                " where trpn.node_status in (1,4) " +
                " and trpn.node_type in (:nodeTypeList) " +
                " and trp.last_modified_date BETWEEN :startTime and :endTime " +
                " and trpku.user_id in (:userId) and trp.tenant_id = :tenantId " +
                " group by trpn.node_status, trpn.node_type;";
        List<Object[]> objects = entityManager.createNativeQuery(sql)
                .setParameter("startTime", startTime)
                .setParameter("endTime", endTime)
                .setParameter("userId", userIdList)
                .setParameter("tenantId", SecurityUtils.getTenantId())
                .setParameter("nodeTypeList", nodeTypeList)
                .getResultList();
        stopWatch.stop();
        if (CollUtil.isEmpty(objects)) {
            log.info("myApplicationCandidates time = {}ms", stopWatch.getTotalTimeMillis());
            return result;
        }
        stopWatch.start("3. handler nodeType count");
        String eliminated = "ELIMINATED";
        String resigned = "RESIGNED";
        objects.forEach(objectArray -> {
            if (Objects.equals(NodeStatus.ACTIVE.toDbValue(), objectArray[0])) {
                //执行到的流程
                NodeType nodeType = NodeType.fromDbValue(getIntegerFromObject(objectArray[1]));
                result.put(nodeType.name(), getIntegerFromObject(objectArray[2]));
                if (nodeType.equals(NodeType.ON_BOARD)){
                    result.put(resigned, getIntegerFromObject(objectArray[3]));
                }
            } else {
                //淘汰了的流程
                if (result.containsKey(eliminated)) {
                    result.put(eliminated, result.get(eliminated) + getIntegerFromObject(objectArray[2]));
                } else {
                    result.put(eliminated, getIntegerFromObject(objectArray[2]));
                }
            }
        });
        stopWatch.stop();
        log.info("myApplicationCandidates time = {}ms \n {}", stopWatch.getTotalTimeMillis(), stopWatch.prettyPrint());
        return result;
    }

    public LinkedHashMap<String, Integer> myApplicationCandidatesWithApplicationIds(Instant startTime, Instant endTime, List<Long> userIdList, List<Long> applicationIds) {
        StopWatch stopWatch = new StopWatch("myApplicationCandidates[2]");
        stopWatch.start("1. search nodeTypeList");
        LinkedHashMap<String, Integer> result = new LinkedHashMap<>(16);
        String nodeTypeSql = "SELECT distinct rpn.node_type " +
                " FROM recruitment_process rp " +
                " INNER JOIN recruitment_process_node rpn ON rp.id = rpn.recruitment_process_id " +
                " where rp.tenant_id = :tenantId";
        List<Integer> nodeTypeList = entityManager.createNativeQuery(nodeTypeSql)
                .setParameter("tenantId", SecurityUtils.getTenantId()).getResultList();
        stopWatch.stop();
        if (CollUtil.isEmpty(nodeTypeList)) {
            log.info("myApplicationCandidates time = {}ms", stopWatch.getTotalTimeMillis());
            return result;
        }
        stopWatch.start("2. nodeType count");
        String sql = " SELECT trpn.node_status, trpn.node_type, count(distinct trpn.talent_recruitment_process_id), count(distinct resign.talent_recruitment_process_id) " +
                " FROM talent_recruitment_process trp " +
                " INNER JOIN talent_recruitment_process_kpi_user trpku ON trpku.talent_recruitment_process_id = trp.id " +
                " INNER JOIN talent_recruitment_process_node trpn ON trpn.talent_recruitment_process_id = trp.id " +
                " LEFT JOIN talent_recruitment_process_resignation resign ON resign.talent_recruitment_process_id = trp.id " +
                " where trpn.node_status in (1,4) " +
                " and trpn.node_type in (:nodeTypeList) " +
                " and trp.last_modified_date BETWEEN :startTime and :endTime " +
                " and trp.id in (:applicationIds) " +
                " and trpku.user_id in (:userId) and trp.tenant_id = :tenantId " +
                " group by trpn.node_status, trpn.node_type;";
        List<Object[]> objects = entityManager.createNativeQuery(sql)
                .setParameter("startTime", startTime)
                .setParameter("endTime", endTime)
                .setParameter("applicationIds", applicationIds)
                .setParameter("userId", userIdList)
                .setParameter("tenantId", SecurityUtils.getTenantId())
                .setParameter("nodeTypeList", nodeTypeList)
                .getResultList();
        stopWatch.stop();
        if (CollUtil.isEmpty(objects)) {
            log.info("myApplicationCandidates time = {}ms", stopWatch.getTotalTimeMillis());
            return result;
        }
        stopWatch.start("3. handler nodeType count");
        String eliminated = "ELIMINATED";
        String resigned = "RESIGNED";
        objects.forEach(objectArray -> {
            if (Objects.equals(NodeStatus.ACTIVE.toDbValue(), objectArray[0])) {
                //执行到的流程
                NodeType nodeType = NodeType.fromDbValue(getIntegerFromObject(objectArray[1]));
                result.put(nodeType.name(), getIntegerFromObject(objectArray[2]));
                if (nodeType.equals(NodeType.ON_BOARD)){
                    result.put(resigned, getIntegerFromObject(objectArray[3]));
                }
            } else {
                //淘汰了的流程
                if (result.containsKey(eliminated)) {
                    result.put(eliminated, result.get(eliminated) + getIntegerFromObject(objectArray[2]));
                } else {
                    result.put(eliminated, getIntegerFromObject(objectArray[2]));
                }
            }
        });
        stopWatch.stop();
        log.info("myApplicationCandidates[2] time = {}ms \n {}", stopWatch.getTotalTimeMillis(), stopWatch.prettyPrint());
        return result;
    }

    public Integer getIntegerFromObject(Object o) {
        return Integer.parseInt(String.valueOf(o));
    }

    public Integer findOnboardedNotInvoiced(Instant startTime, Instant endTime,List<Long> userIdList) {
        String sql = " SELECT count(distinct trp.id) " +
                " FROM talent_recruitment_process trp " +
                " INNER JOIN talent_recruitment_process_node trpn ON trpn.talent_recruitment_process_id = trp.id " +
                " INNER JOIN talent_recruitment_process_onboard_date trpod ON trpod.talent_recruitment_process_id = trp.id " +
                " INNER JOIN talent_recruitment_process_kpi_user trpku ON trpku.talent_recruitment_process_id = trp.id and (trpku.user_role is null or trpku.user_role != 4) " +
                " WHERE trp.last_modified_date BETWEEN :startTime AND :endTime and trp.tenant_id = :tenantId and " +
                " trpku.user_id in (:userId) and trpn.node_type = 60 and trpn.node_status = 1 " +
                " and not exists (select 1 from invoice i where i.job_id = trp.job_id and i.talent_id = trp.talent_id and i.status != 7) " +
                " and not exists (select 1 from t_contractor_invoice tc where tc.job_id = trp.job_id and tc.talent_id = trp.talent_id and tc.invoice_status != 3) ";
        Query query = entityManager.createNativeQuery(sql)
                .setParameter("startTime", startTime)
                .setParameter("endTime", endTime)
                .setParameter("userId", userIdList)
                .setParameter("tenantId", SecurityUtils.getTenantId());
        return ((BigInteger) query.getSingleResult()).intValue();
    }

    public Integer findContractInvoicedPendingPayment(Instant startTime, Instant endTime,List<Long> userIdList) {
        String contractSql = " select count(distinct tci.id) invoiceCount " +
                " from t_contractor_invoice tci " +
                " inner join timesheet_talent_assignment tta on tta.id = tci.assignment_id " +
                " left join t_group_invoice_record tgir on tgir.invoice_id = tci.id " +
                " left join t_group_invoice tgi on tgi.id= tgir.group_invoice_id " +
                " where tci.tenant_id = :tenantId and tgi.group_invoice_status in (5,2,4)" +
                " and tci.created_date BETWEEN :startTime AND :endTime " +
                " and exists(select 1 from talent_recruitment_process trp inner join talent_recruitment_process_kpi_user trpku on trp.id = trpku.talent_recruitment_process_id " +
                "where trpku.user_id in (:userId) and trp.id = tta.talent_recruitment_process_id)";
        Query contractQuery = entityManager.createNativeQuery(contractSql)
                .setParameter("startTime", startTime)
                .setParameter("endTime", endTime)
                .setParameter("userId", userIdList)
                .setParameter("tenantId", SecurityUtils.getTenantId());
        return  ((BigInteger) contractQuery.getSingleResult()).intValue();
    }

    public Integer findFteInvoicedPendingPayment(Instant startTime, Instant endTime,List<Long> userIdList) {
        String fteSql = " select count(distinct i.id) " +
                " from invoice i " +
                " where i.tenant_id = :tenantId and i.status in (1,2,8) and i.invoice_type = " + InvoiceType.FTE.toDbValue() +
                " and i.created_date BETWEEN :startTime AND :endTime " +
                " and exists(select 1 from talent_recruitment_process trp " +
                " inner join talent_recruitment_process_kpi_user trpku on trp.id = trpku.talent_recruitment_process_id " +
                " inner join start s on s.talent_recruitment_process_id = trp.id " +
                " where trpku.user_id in (:userId) and s.id = i.start_id) ";
                Query fteQuery = entityManager.createNativeQuery(fteSql)
                .setParameter("startTime", startTime)
                .setParameter("endTime", endTime)
                .setParameter("userId", userIdList)
                .setParameter("tenantId", SecurityUtils.getTenantId());
        return ((BigInteger) fteQuery.getSingleResult()).intValue();
    }



    public Integer findFteOverdueNotReceived(List<Long> userIdList) {
        String fteSql = " select count(distinct i.id) " +
                " from invoice i " +
                " where i.tenant_id = :tenantId and i.status = " + InvoiceStatus.OVERDUE.toDbValue() + " and i.invoice_type = " + InvoiceType.FTE.toDbValue() +
                " and exists(select 1 from talent_recruitment_process trp " +
                " inner join talent_recruitment_process_kpi_user trpku on trp.id = trpku.talent_recruitment_process_id " +
                " inner join start s on s.talent_recruitment_process_id = trp.id " +
                " where trpku.user_id in (:userId) and s.id = i.start_id) ";
        Query fteQuery = entityManager.createNativeQuery(fteSql)
                .setParameter("userId", userIdList)
                .setParameter("tenantId", SecurityUtils.getTenantId());
        return ((BigInteger) fteQuery.getSingleResult()).intValue();
    }

    public Integer findContractOverdueNotReceived(List<Long> userIdList) {
        String contractSql = " select count(distinct tci.id) " +
                " from t_contractor_invoice tci " +
                " inner join timesheet_talent_assignment tta on tta.id = tci.assignment_id " +
                " left join t_group_invoice_record tgir on tgir.invoice_id = tci.id " +
                " left join t_group_invoice tgi on tgi.id= tgir.group_invoice_id " +
                " where tci.tenant_id = :tenantId and tgi.group_invoice_status = " + GroupInvoiceStatus.OVERDUE.toDbValue() +
                " and exists(select 1 from talent_recruitment_process trp inner join talent_recruitment_process_kpi_user trpku on trp.id = trpku.talent_recruitment_process_id " +
                "where trpku.user_id in (:userId) and trp.id = tta.talent_recruitment_process_id); ";
        Query contractQuery = entityManager.createNativeQuery(contractSql)
                .setParameter("userId", userIdList)
                .setParameter("tenantId", SecurityUtils.getTenantId());
        return ((BigInteger) contractQuery.getSingleResult()).intValue();
    }

    public List<KpiTopVo> kpiTop(Instant startTime, Instant endTime, Integer top, Integer currency) {
        CurrencyRateDay currencyRateDay = currencyRateDayRepository.findFirstByCurrencyIdOrderByRateDayDesc(Long.valueOf(currency)).get();
        String sql = """
                SELECT user_id, user_full_name full_name, ROUND(( fteGp + contactGp ) * :toUsdRate, 2 ) amount
                FROM(
                 	SELECT
                 		user_id,
                 		user_full_name,
                 		COALESCE ( sum( fteGp * percentage * 0.01 / fte_from_usd_rate ), 0 ) AS fteGp,
                 		COALESCE ( sum( contractGp * percentage * 0.01 / contract_from_usd_rate ), 0 ) AS contactGp
                 	FROM(
                 		SELECT
                 			sc.user_id,
                 			sc.user_full_name,
                 			scr.total_bill_amount contractGp,
                 			sfr.total_bill_amount fteGp,
                 			sc.percentage percentage,
                 		    IF( cecd.id IS NULL, cec.to_usd_rate, cecd.from_usd_rate_mid ) contract_from_usd_rate,
                 		    IF( fecd.id IS NULL, fec.to_usd_rate, fecd.from_usd_rate_mid ) fte_from_usd_rate
                 		FROM
                 			start s
                 			INNER JOIN start_commission sc ON sc.start_id = s.id
                 			LEFT JOIN start_contract_rate scr ON scr.start_id = s.id
                 			LEFT JOIN start_fte_rate sfr ON sfr.start_id = s.id
                 			LEFT JOIN enum_currency cec ON cec.id = scr.currency
                 			LEFT JOIN currency_rate_day cecd ON cecd.rate_day = DATE_FORMAT( IF ( scr.start_date IS NULL, s.start_date, scr.start_date ), '%Y-%m-%d' )
                 			AND scr.currency = cecd.currency_id
                 			LEFT JOIN enum_currency fec ON fec.id = sfr.currency
                 			LEFT JOIN currency_rate_day fecd ON fecd.rate_day = DATE_FORMAT( s.start_date, '%Y-%m-%d' )
                 			AND sfr.currency = fecd.currency_id
                 		WHERE
                 			s.start_date BETWEEN :startTime AND :endTime 
                 			AND s.STATUS != - 1
                 			AND s.tenant_id = :tenantId ) temp
                 	GROUP BY
                 		user_id) sumTemp
                 ORDER BY
                 	amount DESC
                    limit :top
                 """;
        Query query = entityManager.createNativeQuery(sql, KpiTopVo.class)
                .setParameter("startTime", startTime)
                .setParameter("endTime", endTime)
                .setParameter("tenantId", SecurityUtils.getTenantId())
                .setParameter("top", ObjectUtil.isEmpty(top)? 10: top)
                .setParameter("toUsdRate", currencyRateDay.getRateMid());
        return query.getResultList();
    }

    public List<InvoiceXxlJobVO> getXxlJobVOListByFteInvoiceIdList(List<Long> fteInvoiceIdList) {
        String sql = " SELECT kpi.user_id, i.id invoice_id, i.invoice_no invoice_no, u.custom_timezone as timezone, i.due_date due_date, t.full_name, t.id talent_id " +
                " FROM invoice i inner join start s on s.id = i.start_id " +
                " inner join talent_recruitment_process_kpi_user kpi on kpi.talent_recruitment_process_id = s.talent_recruitment_process_id and (kpi.user_role is null or kpi.user_role != 4)" +
                " inner join user u on u.id = kpi.user_id " +
                " inner join talent_recruitment_process trp on trp.id = kpi.talent_recruitment_process_id " +
                " inner join talent t on t.id = trp.talent_id " +
                " where i.id in (:fteInvoiceIdList) " +
                " group by i.id, kpi.user_id ";
        Map<String, Object> paramMap = new HashMap<>(16);
        paramMap.put("fteInvoiceIdList", fteInvoiceIdList);
        List<Map<String, Object>> mapList = doSearchDataWithMap(sql, paramMap);
        if (CollUtil.isEmpty(mapList)) {
            return new ArrayList<>();
        }
        return convertEntity(mapList, InvoiceXxlJobVO.class);
    }

    public List<InvoiceXxlJobVO> getXxlJobVOListByContractIdList(List<Long> contractIdList) {
        String sql = " SELECT kpi.user_id, tci.id invoice_id, tci.invoice_number invoice_no, u.custom_timezone timezone," +
                " DATE_ADD(tgi.invoice_date, INTERVAL tgi.due_within_days DAY) due_date, t.full_name, t.id talent_id"  +
                " FROM t_contractor_invoice tci " +
                " inner join timesheet_talent_assignment tta on tta.id = tci.assignment_id " +
                " inner join talent_recruitment_process_kpi_user kpi on kpi.talent_recruitment_process_id = tta.talent_recruitment_process_id and kpi.user_role != 4 " +
                " inner join t_group_invoice_record tgir on tgir.invoice_id = tci.id and tgir.`status` = 1 " +
                " inner join t_group_invoice tgi on tgir.group_invoice_id = tgi.id " +
                " inner join user u on u.id = kpi.user_id " +
                " inner join talent_recruitment_process trp on trp.id = kpi.talent_recruitment_process_id " +
                " inner join talent t on t.id = trp.talent_id " +
                " where tci.id in (:contractIdList) " +
                " group by kpi.user_id, tci.id ";
        Map<String, Object> paramMap = new HashMap<>(16);
        paramMap.put("contractIdList", contractIdList);
        List<Map<String, Object>> mapList = doSearchDataWithMap(sql, paramMap);
        if (CollUtil.isEmpty(mapList)) {
            return new ArrayList<>();
        }
        return convertEntity(mapList, InvoiceXxlJobVO.class);
    }

    public BigDecimal findAverageYieldPerUnit(Instant startTime, Instant endTime, Integer currency) {
        EnumCurrency enumCurrency = enumCurrencyService.findEnumCurrencyById(currency);
        String sql = " select ROUND(AVG(gp) * :toUsdRate, 2) avg_amount from (" +
                " SELECT (COALESCE(fte.total_amount,0) + COALESCE(contract.gp,0)) * ec.from_usd_rate as gp " +
                " FROM talent_recruitment_process trp " +
                " inner join talent_recruitment_process_node trpn on trpn.talent_recruitment_process_id = trp.Id and trpn.node_status = 1 and trpn.node_type = 60 " +
                " inner join talent_recruitment_process_onboard_date trpod on trpod.talent_recruitment_process_id = trp.id " +
                " inner join talent_recruitment_process_ipg_agreed_pay_rate rate on rate.talent_recruitment_process_id = trp.id " +
                " inner join enum_currency ec on ec.id = trpod.currency " +
                " inner join talent_recruitment_process_kpi_user kpi on kpi.talent_recruitment_process_id = trp.id " +
                " left join talent_recruitment_process_offer_fee_charge fte on fte.talent_recruitment_process_id = trp.id " +
                " left join talent_recruitment_process_ipg_contract_fee_charge contract on contract.talent_recruitment_process_id = trp.id " +
                " where kpi.user_id = :userId and trpod.onboard_date BETWEEN :startTime AND :endTime " +
                " group by trp.id) temp ;";
        Query query = entityManager.createNativeQuery(sql)
                .setParameter("userId", SecurityUtils.getUserId())
                .setParameter("startTime", startTime)
                .setParameter("endTime", endTime)
                .setParameter("toUsdRate", enumCurrency.getToUsdRate());
        return (BigDecimal) query.getSingleResult();
    }

    public BigDecimal findTeamContractAmountPaid(Instant startTime, Instant endTime, Integer currency,List<Long> userIdList) {
        EnumCurrency enumCurrency = enumCurrencyService.findEnumCurrencyById(currency);
        String sql = " select ROUND(sum(gp) * :toUsdRate, 2) avg_amount from ( " +
                " SELECT COALESCE(tci.total_amount,0)  * ec.from_usd_rate * (kpi.percentage/100) as gp " +
                " FROM t_contractor_invoice tci inner join t_group_invoice_record tgir on tgir.invoice_id = tci.id " +
                " inner join t_group_invoice tgi on tgi.id = tgir.group_invoice_id " +
                " inner join timesheet_talent_assignment tta on tta.id = tci.assignment_id " +
                " inner join talent_recruitment_process_kpi_user kpi on kpi.talent_recruitment_process_id = tta.talent_recruitment_process_id " +
                " inner join enum_currency ec on ec.id = tgi.currency_id " +
                " inner join permission_user_team put on put.user_id = kpi.user_id " +
                " where tgi.group_invoice_status = 3 and tci.created_date BETWEEN :startTime AND :endTime " +
                " and exists ( select 1 from permission_user_team t where t.is_primary = 1 and t.team_id = put.team_id and t.user_id in (:userId) ) " +
                " ) temp ;";
        Query query = entityManager.createNativeQuery(sql)
                .setParameter("userId", userIdList)
                .setParameter("startTime", startTime)
                .setParameter("endTime", endTime)
                .setParameter("toUsdRate", enumCurrency.getToUsdRate());
        Object result = query.getSingleResult();
        return new BigDecimal(String.valueOf(ObjectUtil.isEmpty(result)? 0: result));
    }

    public TeamPerformanceVO findTeamFteAmount(Instant startTime, Instant endTime, Integer currency,List<Long> userIdList) {
        EnumCurrency enumCurrency = enumCurrencyService.findEnumCurrencyById(currency);
        String sql = " select ROUND(sum(received_amount) * :toUsdRate, 2) received_amount , ROUND(sum(balance)  * :toUsdRate, 2) balance " +
                " from (SELECT vil.received_amount * ec.from_usd_rate * (kpi.percentage/100) received_amount, " +
                " (vil.due_amount - COALESCE(vil.received_amount,0)) * ec.from_usd_rate * (kpi.percentage/100) balance " +
                " FROM invoice i " +
                " INNER JOIN START s ON s.id = i.start_id " +
                " INNER JOIN talent_recruitment_process_kpi_user kpi ON kpi.talent_recruitment_process_id = s.talent_recruitment_process_id " +
                " INNER JOIN enum_currency ec ON ec.id = i.currency " +
                " INNER JOIN permission_user_team put ON put.user_id = kpi.user_id " +
                " INNER JOIN view_invoice_list vil ON vil.id = i.id " +
                " WHERE i.STATUS IN ( 0, 1, 2, 8) AND i.created_date BETWEEN :startTime AND :endTime " +
                " and exists ( select 1 from permission_user_team t where t.is_primary = 1 and t.team_id = put.team_id and t.user_id in (:userId )) " +
                " ) temp ;";
        Query query = entityManager.createNativeQuery(sql)
                .setParameter("userId", userIdList)
                .setParameter("startTime", startTime)
                .setParameter("endTime", endTime)
                .setParameter("toUsdRate", enumCurrency.getToUsdRate());
        List<Object[]> objects = query.getResultList();
        TeamPerformanceVO teamPerformanceVO = new TeamPerformanceVO();
        if (CollUtil.isNotEmpty(objects)) {
            if (ObjectUtil.isNotEmpty(objects.get(0)[0])) {
                teamPerformanceVO.setFtePaid(new BigDecimal(String.valueOf(objects.get(0)[0])));
            }
            if (ObjectUtil.isNotEmpty(objects.get(0)[1])) {
                teamPerformanceVO.setFteUnPaid(new BigDecimal(String.valueOf(objects.get(0)[1])));
            }
        }
        return teamPerformanceVO;
    }

    public BigDecimal findTeamContractAmountOutstanding(Instant startTime, Instant endTime, Integer currency,List<Long> userIdList) {
        EnumCurrency enumCurrency = enumCurrencyService.findEnumCurrencyById(currency);
        String sql = " select ROUND(sum(gp) * :toUsdRate, 2) avg_amount from ( " +
                " SELECT COALESCE(tci.total_amount,0) * ec.from_usd_rate * (kpi.percentage/100) as gp " +
                " FROM t_contractor_invoice tci inner join t_group_invoice_record tgir on tgir.invoice_id = tci.id " +
                " inner join t_group_invoice tgi on tgi.id = tgir.group_invoice_id " +
                " inner join timesheet_talent_assignment tta on tta.id = tci.assignment_id " +
                " inner join talent_recruitment_process_kpi_user kpi on kpi.talent_recruitment_process_id = tta.talent_recruitment_process_id " +
                " inner join enum_currency ec on ec.id = tgi.currency_id " +
                " INNER JOIN permission_user_team put ON put.user_id = kpi.user_id " +
                " where tgi.group_invoice_status in (5,2,4) and tci.created_date BETWEEN :startTime AND :endTime " +
                " and exists ( select 1 from permission_user_team t where t.is_primary = 1 and t.team_id = put.team_id and t.user_id in (:userId) ) " +
                " ) temp ;";
        Query query = entityManager.createNativeQuery(sql)
                .setParameter("userId", userIdList)
                .setParameter("startTime", startTime)
                .setParameter("endTime", endTime)
                .setParameter("toUsdRate", enumCurrency.getToUsdRate());
        Object result = query.getSingleResult();
        return new BigDecimal(String.valueOf(ObjectUtil.isEmpty(result)? 0: result));
    }

    public Boolean findInvoiceByTalentRecruitmentProcessId(Long talentRecruitmentProcessId) {
        String sql = " select count(1) from talent_recruitment_process trp " +
                " where trp.id = :talentRecruitmentProcessId " +
                " AND not exists (select 1 from invoice i where i.job_id = trp.job_id and i.talent_id = trp.talent_id and i.status != 7) " +
                " AND not exists (select 1 from t_contractor_invoice tc where tc.job_id = trp.job_id and tc.talent_id = trp.talent_id and tc.invoice_status != 3) ";
        Query query = entityManager.createNativeQuery(sql)
                .setParameter("talentRecruitmentProcessId", talentRecruitmentProcessId);
        return ((BigInteger) query.getSingleResult()).intValue() > 0;
    }

    public Long findTalentRecruitmentProcessIdByFteInvoiceId(Long fteInvoiceId) {
        String sql = " select trp.id from talent_recruitment_process trp " +
                " where exists (select 1 from invoice i inner join start s on i.start_id = s.id " +
                " and s.talent_recruitment_process_id = trp.id and i.id = :fteInvoiceId) ";
        Query query = entityManager.createNativeQuery(sql)
                .setParameter("fteInvoiceId", fteInvoiceId);
        return ((BigInteger) query.getSingleResult()).longValue();
    }

    public List<Long> findTalentRecruitmentProcessIdByContractInvoiceIdList(List<Long> contractInvoiceIdList) {
        List<Long> talentRecruitmentProcessIdList = new ArrayList<>();
        String sql = " select distinct trp.id from talent_recruitment_process trp " +
                " where exists (select 1 from t_contractor_invoice tci inner join timesheet_talent_assignment tta on tta.id = tci.assignment_id " +
                " and tta.talent_recruitment_process_id = trp.id and tci.id in (:contractInvoiceIdList) ) ";
        Query query = entityManager.createNativeQuery(sql).setParameter("contractInvoiceIdList", contractInvoiceIdList);
        List<Object[]> objects = query.getResultList();
        if (CollUtil.isEmpty(objects)) {
            return talentRecruitmentProcessIdList;
        }
        objects.forEach(obj -> talentRecruitmentProcessIdList.add(Long.parseLong(String.valueOf(obj[0]))));
        return talentRecruitmentProcessIdList;
    }


    public List<Map<String, Object>> doSearchDataWithMap(String queryStr, Map<String, Object> map) {
        entityManager.clear();
        Query query = entityManager.createNativeQuery(queryStr);
        Method method = ReflectUtil.getMethod(Query.class, "setParameter", String.class, Object.class);
        Optional.ofNullable(map).ifPresent(m -> m.forEach((k, v) -> ReflectUtil.invoke(query, method, k, v)));
        query.unwrap(SQLQuery.class).setResultTransformer(Transformers.ALIAS_TO_ENTITY_MAP);
        return query.getResultList();
    }

    public <T> List<T> convertEntity(List<Map<String, Object>> mapList, Class<T> clazz) {
        if (CollUtil.isEmpty(mapList)) {
            new ArrayList<>();
        }
        return JSON.parseArray(JSON.toJSONString(mapList), clazz);
    }

    public MyJobVo findMyJobs(List<Long> userIdList, Long tenantId, Instant startTime, Instant endTime, Long projectId) {
        MyJobVo vo = new MyJobVo();
        String sql = "SELECT STATUS status , count(DISTINCT id) count " +
                " FROM ( " +
                " SELECT j.STATUS, j.id  " +
                " FROM recruitment_process rp INNER JOIN job j ON j.recruitment_process_id = rp.id  " +
                " WHERE rp.job_type != :jobType AND j.STATUS IN ( 0, 5 ) and j.created_date BETWEEN :startTime AND :endTime " +
                " AND j.puser_id in ( :userId ) AND j.tenant_id = :tenantId " +
                " AND j.pteam_id != :projectId " +
                " UNION " +
                " SELECT j.STATUS, j.id  " +
                " FROM  recruitment_process rp  INNER JOIN job j ON j.recruitment_process_id = rp.id " +
                " LEFT JOIN user_job_relation ujr ON ujr.job_id = j.id AND ujr.status = 1 " +
                " WHERE rp.job_type != :jobType  AND j.STATUS IN ( 0, 5 ) and j.created_date BETWEEN :startTime AND :endTime " +
                " AND ujr.user_id in ( :userId ) AND j.tenant_id = :tenantId " +
                " AND j.pteam_id != :projectId " +
                " UNION " +
                " SELECT j.STATUS, j.id  " +
                " FROM recruitment_process rp INNER JOIN job j ON j.recruitment_process_id = rp.id " +
                " LEFT JOIN talent_recruitment_process trp ON trp.job_id = j.id " +
                " LEFT JOIN talent_recruitment_process_kpi_user trpku ON trpku.talent_recruitment_process_id = trp.id  " +
                " AND trpku.user_role != 4  " +
                " WHERE rp.job_type != :jobType AND j.STATUS IN ( 0, 5 ) and j.created_date BETWEEN :startTime AND :endTime " +
                " AND trpku.user_id in ( :userId ) AND j.tenant_id = :tenantId " +
                " AND j.pteam_id != :projectId " +
                ") temp  " +
                "GROUP BY " +
                "status";
        List<Object[]> objectList = entityManager.createNativeQuery(sql)
                .setParameter("startTime", startTime)
                .setParameter("endTime", endTime)
                .setParameter("userId", userIdList)
                .setParameter("tenantId", SecurityUtils.getTenantId())
                .setParameter("projectId", projectId)
                .setParameter("jobType", JobType.PAY_ROLL.toDbValue())
                .getResultList();
        if (ObjectUtil.isEmpty(objectList)) {
            return vo;
        }
        objectList.forEach(objectArray -> {
            if (Objects.equals(JobStatus.OPEN.toDbValue(), objectArray[0])) {
                vo.setOpenNums(Integer.parseInt(String.valueOf(objectArray[1])));
            } else {
                vo.setFilledNums(Integer.parseInt(String.valueOf(objectArray[1])));
            }
        });
        return vo;
    }


    public Integer findNotRecommendedWithin14Days(List<Long> userIdList, Long tenantId, Long projectId,Integer day) {
        String sql = """
         select count(DISTINCT temp.id) from
         (
         SELECT j.id, TIMESTAMPDIFF(DAY, max(trp.created_date), now()) diff, TIMESTAMPDIFF(DAY, j.created_date, now()) createDdiff
         FROM recruitment_process rp
         INNER JOIN job j ON j.recruitment_process_id = rp.id
         left JOIN job_user_relation jur ON jur.job_id = j.id
         LEFT JOIN talent_recruitment_process trp ON trp.job_id = j.id
         WHERE j.STATUS = 0 AND jur.user_id in ( :userId ) AND j.tenant_id = :tenantId
         AND j.pteam_id != :projectId
         GROUP BY j.id
         having diff >= :diff or (diff is null and createDdiff <= :diff)
         union all
         SELECT j.id, TIMESTAMPDIFF(DAY, max(trp.created_date), now()) diff, TIMESTAMPDIFF(DAY, j.created_date, now()) createDdiff
         FROM recruitment_process rp
         INNER JOIN job j ON j.recruitment_process_id = rp.id
         LEFT JOIN talent_recruitment_process trp ON trp.job_id = j.id
         LEFT JOIN talent_recruitment_process_kpi_user kpi ON kpi.talent_recruitment_process_id = trp.id
         WHERE j.STATUS = 0 AND kpi.user_id in ( :userId ) AND j.tenant_id = :tenantId
         AND j.pteam_id != :projectId
         GROUP BY j.id
         having diff >= :diff or (diff is null and createDdiff <= :diff)
         ) temp
        """;
        Query query = entityManager.createNativeQuery(sql)
                .setParameter("userId", userIdList)
                .setParameter("tenantId", SecurityUtils.getTenantId())
                .setParameter("diff", day)
                .setParameter("projectId", projectId);
        return ((BigInteger) query.getSingleResult()).intValue();
    }

    public Integer findNotInterviewWithin14Days(List<Long> userIdList, Long tenantId, Long projectId,Integer day) {
        String sql = """ 
                 select count(1) from 
                 (SELECT j.id, TIMESTAMPDIFF(DAY, max(trpi.created_date), now()) diff, TIMESTAMPDIFF(DAY, j.created_date, now()) createDdiff 
                 FROM recruitment_process rp
                 INNER JOIN job j ON j.recruitment_process_id = rp.id
                 LEFT JOIN talent_recruitment_process trp ON trp.job_id = j.id 
                 left join talent_recruitment_process_interview trpi on trpi.talent_recruitment_process_id = trp.id
                 WHERE j.STATUS = 0 AND j.puser_id in ( :userId ) AND j.tenant_id = :tenantId 
                 AND j.pteam_id != :projectId 
                 GROUP BY j.id 
                 having diff >= :diff or (diff is null and createDdiff <= :diff)) temp 
        """;
        Query query = entityManager.createNativeQuery(sql)
                .setParameter("userId", userIdList)
                .setParameter("tenantId", SecurityUtils.getTenantId())
                .setParameter("diff", day)
                .setParameter("projectId", projectId);
        return ((BigInteger) query.getSingleResult()).intValue();
    }

    public JobProject findFirstByTenantId(Long tenantId) {
        String sql = "select * from job_project where tenant_id = :tenantId limit 1";
        Query query = entityManager.createNativeQuery(sql,JobProject.class);
        query.setParameter("tenantId", tenantId);
        List<JobProject> jobProjects = query.getResultList();
        if(jobProjects.isEmpty()){
            return null;
        }
        return jobProjects.get(0);
    }
}
