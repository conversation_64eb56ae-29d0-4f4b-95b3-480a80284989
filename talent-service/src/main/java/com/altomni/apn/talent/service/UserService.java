package com.altomni.apn.talent.service;

import com.altomni.apn.common.domain.enumeration.user.Status;
import com.altomni.apn.common.domain.user.User;
import com.altomni.apn.common.dto.user.UserBriefDTO;
import com.altomni.apn.common.dto.user.UserUidNameDTO;
import com.altomni.apn.common.enumeration.permission.Module;
import com.altomni.apn.user.config.UserPersonalizationConfig;
import com.altomni.apn.user.domain.user.CreditTransaction;
import com.altomni.apn.user.service.dto.customconfig.TalentColumnConfigDTO;
import com.altomni.apn.user.service.dto.permission.PermissionTeamUserDTO;
import com.altomni.apn.user.service.dto.user.CreditTransactionDTO;
import com.altomni.apn.user.web.rest.vm.permission.PermissionTeamMemberSearchVM;
import com.altomni.apn.user.web.rest.vm.permission.PermissionUserTeamPermissionVM;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;
import java.util.Set;

@Component
@FeignClient(value = "user-service")
public interface UserService {

    @PostMapping("/user/api/v3/permissions/teams/users/team-users")
    ResponseEntity<List<PermissionTeamUserDTO>> getTeamUsersByPermissionTeamIdIn(@RequestBody PermissionTeamMemberSearchVM teamMemberSearchVM);

    @GetMapping("/user/api/v3/users/find-user/tenant-and-authority")
    ResponseEntity<User> findFirstUserByTenantIdAndAuthorityName(@RequestParam("tenantId") Long tenantId, @RequestParam("authorityName") String authorityName);

    @GetMapping("/user/api/v3/permissions/users/{userId}/data-permissions")
    ResponseEntity<PermissionUserTeamPermissionVM> getDataPermissionsByUserId(@PathVariable("userId") Long userId);

    @GetMapping("/user/api/v3/users/common/tenant-param/{paramKey}/value")
    ResponseEntity<Integer> getTenantParamValue(@PathVariable("paramKey") String paramKey);

    @GetMapping("/user/api/v3/users/{id}")
    ResponseEntity<User> getUserById(@PathVariable("id") Long userId);

    @PostMapping("/user/api/v3/users/get-all-activated-user-by-ids")
    ResponseEntity<List<User>> findALLByIdInAndActivated(@RequestBody List<Long> ids);

    @PostMapping("/user/api/v3/users/all-by-ids")
    ResponseEntity<List<User>> findByIds(@RequestBody List<Long> ids);

    @PostMapping("/user/api/v3/credit-transactions/find/tenant-id-and-status-and-search-es-ids")
    ResponseEntity<List<CreditTransaction>> findAllCreditTransactionByTenantIdAndStatusAndSearchEsIdIn(@RequestParam("tenantId") Long tenantId, @RequestParam("status") Status status, @RequestBody List<String> searchEsIds);
    @PostMapping("/user/api/v3/credit-transactions/find/talent-id")
    ResponseEntity<CreditTransaction> findCreditTransactionByTenantIdAndStatusAndTalentIdIs(@RequestParam("tenantId") Long tenantId, @RequestParam("status") Status status, @RequestParam("talentId") Long talentId);

    @GetMapping("/user/api/v3/credit-transactions/find/profile-id-and-tenant-id-and-status")
    ResponseEntity<CreditTransaction> findByProfileIdAndTenantIdAndStatus(@RequestParam("profileId") String profileId, @RequestParam("tenantId") Long tenantId, @RequestParam("status") Status status);

    @PostMapping("/user/api/v3/credit-transactions/find/tenant-id-and-status-and-profile-ids")
    ResponseEntity<List<CreditTransaction>> findAllCreditTransactionByTenantIdAndStatusAndProfileIdIn(@RequestParam("tenantId") Long tenantId, @RequestParam("status") Status status, @RequestBody List<String> profileId);

    @GetMapping("/user/api/v3/credit-transactions/find/tenant-id-and-status")
    ResponseEntity<List<CreditTransaction>> findCreditTransactionByTenantIdAndStatus(@RequestParam("tenantId") Long tenantId, @RequestParam("status") Status status);

    @PutMapping("/user/api/v3/credit-transactions/commonPool")
    ResponseEntity<CreditTransactionDTO> updateCreditTalentIdForCommonPool(@RequestBody CreditTransactionDTO creditTransactionDTO);

    @PostMapping("/user/api/v3/users/all-brief-by-ids/including-inactive")
    ResponseEntity<List<UserBriefDTO>> getAllBriefUsersByIds(@RequestBody List<Long> ids);

    @GetMapping("/user/api/v3/talents/preferences/talent-column")
    ResponseEntity<TalentColumnConfigDTO> getTalentColumn();

    @GetMapping("/user/api/v3/talents/preferences/relate-job-folder-column")
    ResponseEntity<TalentColumnConfigDTO> getTalentRelateJobFolderColumnConfig();
    @GetMapping("/user/api/v3/talents/preferences/search-folder-column")
    ResponseEntity<TalentColumnConfigDTO> getTalentSearchFolderColumnConfig();

    @GetMapping("/user/api/v3/talents/preferences/talent-database-column")
    ResponseEntity<TalentColumnConfigDTO> getTalentCommonPoolColumn();

    @PostMapping("/user/api/v3/users/get-user-names-by-uid-in")
    ResponseEntity<Map<String, UserUidNameDTO>> findUsersByUidIn(@RequestBody List<String> uids);

    @GetMapping("/user/api/v3/permissions/users/{userId}/all-data-permissions")
    ResponseEntity<PermissionUserTeamPermissionVM.PermissionDetail> getAllDataPermissionsByUserId(@PathVariable("userId") Long userId);

    @PostMapping("/user/api/v3/permissions/users/{userId}/all-data-permissions")
    ResponseEntity<PermissionUserTeamPermissionVM.PermissionDetail> getAllDataPermissionsByTenantIdAndUserId(@RequestBody Long tenantId, @PathVariable("userId") Long userId);

    @GetMapping("/user/api/v3/talents/config/talent-form")
    ResponseEntity<String> getTalentFormConfig();

    @PutMapping("/user/api/v3/user/preferences/personalization")
    ResponseEntity<Void> updatePersonalizationConfig(UserPersonalizationConfig config);

    @GetMapping("/user/api/v3/user/preferences/personalization")
    ResponseEntity<UserPersonalizationConfig> getPersonalizationConfig();

    @GetMapping("/user/api/v3//permissions/teams/user/{userId}/upper/team-leaders")
    ResponseEntity<Set<Long>> getUpperTeamLeadersByUserId(@PathVariable("userId") Long userId);

    @GetMapping("/user/api/v3/users/find-by-id")
    ResponseEntity<UserBriefDTO> findUserBriefById(@RequestParam("id") Long id);

    @GetMapping("/user/api/v3//users/all-brief")
    ResponseEntity<List<UserBriefDTO>> getAllBriefUsers();

    @GetMapping("/user/api/v3/users/all-brief-with-permission/{module}")
    ResponseEntity<List<UserBriefDTO>> getAllBriefUsersWithPermissionByType(@PathVariable("module") Module module);

    @GetMapping("/user/api/v3/permissions/teams/level1-team-profit-leader/user/{userId}")
    ResponseEntity<Long> findProfitTeamLeaderIdByUserId(@PathVariable("userId") Long userId);
}
