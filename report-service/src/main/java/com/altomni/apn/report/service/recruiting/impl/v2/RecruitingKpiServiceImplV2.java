package com.altomni.apn.report.service.recruiting.impl.v2;

import cn.hutool.core.util.BooleanUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.nacos.common.utils.ExceptionUtil;
import com.altomni.apn.common.dto.permission.TeamDataPermissionRespDTO;
import com.altomni.apn.common.dto.recruiting.RecruitingKpiReportSearchDto;
import com.altomni.apn.common.dto.recruiting.StageKpiReportDto;
import com.altomni.apn.common.enumeration.recruiting.RecruitingKpiDateType;
import com.altomni.apn.common.enumeration.recruiting.RecruitingKpiGroupByFieldType;
import com.altomni.apn.common.utils.FutureExceptionUtil;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.common.vo.recruiting.RecruitingKpiByCompanyVO;
import com.altomni.apn.common.vo.recruiting.RecruitingKpiByUserVO;
import com.altomni.apn.common.vo.recruiting.RecruitingKpiCommonCountVO;
import com.altomni.apn.common.vo.recruiting.v2.KpiReportCompanyVO;
import com.altomni.apn.common.vo.recruiting.v2.RecruitingKpiNoteCountVO;
import com.altomni.apn.common.vo.recruiting.v2.ReserveInterviewVO;
import com.altomni.apn.report.repository.RecruitingKpiUserRepository;
import com.altomni.apn.report.repository.v2.RecruitingKpiUserRepositoryV2;
import com.altomni.apn.report.service.recruiting.RecruitingKpiService;
import com.altomni.apn.report.service.recruiting.impl.RecruitingKpiBaseServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StopWatch;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Slf4j
@Service("recruitingKpiServiceV2")
public class RecruitingKpiServiceImplV2 extends RecruitingKpiBaseServiceImpl implements RecruitingKpiService {

    @Resource
    private RecruitingKpiUserRepositoryV2 recruitingKpiUserRepositoryV2;

    @Resource
    private RecruitingKpiUserRepository recruitingKpiUserRepository;

    @Override
    public List<RecruitingKpiByUserVO> searchRecruitingKpiReportByUser(RecruitingKpiReportSearchDto searchDto) {
        log.info("[apn @{}] search recruiting kpi report by user, param = {}", SecurityUtils.getUserId(), JSONUtil.toJsonStr(searchDto));
        StopWatch stopWatch = new StopWatch("recruiting kpi report by user");

        stopWatch.start("[1] search permission data task");
        TeamDataPermissionRespDTO teamDTO = getPermissionDTOAndSetCommonParam(searchDto);
        stopWatch.stop();
        stopWatch.start("[2] search job、talent、application、note、company info、crm company info data task");
        Function<RecruitingKpiCommonCountVO, String> function = getMapKey(searchDto.getGroupByFieldList());
        boolean skipQuery = searchDto.isXxlJobFlag() || searchDto.isE5ReportFlag();

        var applicationFuture = CompletableFuture.supplyAsync(() -> recruitingKpiUserRepositoryV2.searchApplicationKpiData(searchDto, teamDTO)
                .parallelStream().collect(Collectors.toConcurrentMap(function, a -> a)), starRocksExecutor);

        var reserveInterviewFuture = CompletableFuture.supplyAsync(() -> {
            if (RecruitingKpiDateType.ADD.equals(searchDto.getDateType())) {
                return Collections.<String, ReserveInterviewVO>emptyMap();
            }
            return recruitingKpiUserRepositoryV2.searchReserveInterviewKpiData(searchDto, teamDTO)
                    .parallelStream().collect(Collectors.toConcurrentMap(function, a -> a));
        }, starRocksExecutor);

        var noteFuture = CompletableFuture.supplyAsync(() -> {
            List<RecruitingKpiNoteCountVO> voList =  searchDto.isXxlJobFlag() ? List.of() : recruitingKpiUserRepositoryV2.searchNoteKpiData(searchDto, teamDTO);
            return voList.parallelStream().collect(Collectors.toConcurrentMap(function, a -> a));
        }, starRocksExecutor);

        var companyFuture = CompletableFuture.supplyAsync(() -> {
            List<KpiReportCompanyVO> voList = skipQuery ? List.of() : recruitingKpiUserRepositoryV2.searchCompanyKpiData(searchDto, teamDTO);
            return voList.parallelStream().collect(Collectors.toConcurrentMap(function, a -> a));
        }, starRocksExecutor);

        //job
        var jobFuture = CompletableFuture.supplyAsync(() -> recruitingKpiUserRepository.searchJobKpiData(searchDto, teamDTO)
                .parallelStream().collect(Collectors.toConcurrentMap(function, a -> a)), starRocksExecutor);
        //talent
        var talentFuture = CompletableFuture.supplyAsync(() -> recruitingKpiUserRepository.searchTalentKpiData(searchDto, teamDTO)
                .parallelStream().collect(Collectors.toConcurrentMap(function, a -> a)), starRocksExecutor);

        CompletableFuture.allOf(applicationFuture, noteFuture, companyFuture, jobFuture, talentFuture).exceptionally(FutureExceptionUtil::handleFutureException);

        var applicationMap = applicationFuture.join();
        var reserveInterviewMap = reserveInterviewFuture.join();
        var noteMap = noteFuture.join();
        var companyMap = companyFuture.join();
        var jobMap = jobFuture.join();
        var talentMap = talentFuture.join();
        stopWatch.stop();

        Set<String> keySet = Stream.of(applicationMap, reserveInterviewMap, noteMap, companyMap, jobMap, talentMap).parallel()
                .flatMap(map -> map.keySet().stream())
                .collect(Collectors.toSet());

        stopWatch.start("[4] assemble data task");
        List<StageKpiReportDto> stageSearchFilter = addStageFilter(searchDto);

        CopyOnWriteArrayList<RecruitingKpiByUserVO> voList = new CopyOnWriteArrayList<>();
        keySet.parallelStream().forEach(key -> {
            try {
                RecruitingKpiByUserVO vo = new RecruitingKpiByUserVO();
                setGroupByField(vo, key, searchDto.getGroupByFieldList());
                setVoNumV2(vo, jobMap, talentMap, applicationMap, reserveInterviewMap, noteMap, companyMap, key, searchDto);
                voList.add(vo);
            } catch (Exception e) {
                log.error(" search kpi by user assemble data is error, msg = {}", ExceptionUtil.getAllExceptionMsg(e));
                throw e;
            }
        });
        stopWatch.stop();

        List<RecruitingKpiGroupByFieldType> groupByFieldList = searchDto.getGroupByFieldList();
        if (BooleanUtil.isFalse(CollectionUtils.isEmpty(groupByFieldList))) {
            this.appendEmptyData(searchDto.getUser(), groupByFieldList, voList, searchDto.getTeamIdList(), searchDto.getUserIdList(), teamDTO);
        }
        stopWatch.start("[4] search filter data task");
        CopyOnWriteArrayList<RecruitingKpiByUserVO> resultVoList = new CopyOnWriteArrayList<>();
        voList.forEach(vo -> {
            if (checkSearchFilter(vo, stageSearchFilter, searchDto.getApplicationStatusType(), searchDto.getStayedOverList())) {
                resultVoList.add(vo);
            }
        });
        stopWatch.stop();
        log.info("[apn @{}] searchRecruitingKpiReportByUser time = {}ms \n {}", searchDto.getSearchUserId(), stopWatch.getTotalTimeMillis(), stopWatch.prettyPrint());

        return sortVoListByFields(resultVoList, groupByFieldList);
    }

    @Override
    public List<RecruitingKpiByUserVO> searchRecruitingKpiReportByUserForE5(RecruitingKpiReportSearchDto searchDto) {
        return List.of();
    }


    @Override
    public List<RecruitingKpiByCompanyVO> searchRecruitingKpiReportByCompany(RecruitingKpiReportSearchDto searchDto) {
        //todo impl this
        return List.of();
    }


}
