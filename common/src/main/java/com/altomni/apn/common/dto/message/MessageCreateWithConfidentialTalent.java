package com.altomni.apn.common.dto.message;

import com.altomni.apn.common.domain.enumeration.RateUnitType;
import com.altomni.apn.common.dto.RangeDTO;
import com.altomni.apn.common.dto.address.LocationDTO;
import com.altomni.apn.common.dto.talent.TalentDTOV3;
import com.altomni.apn.common.dto.talent.TalentEducationDTO;
import com.altomni.apn.common.dto.talent.TalentExperienceDTO;
import lombok.Getter;
import lombok.Setter;

import java.util.List;
import java.util.Set;

@Getter
@Setter
public class MessageCreateWithConfidentialTalent {

    private Long tenantId;

    private Long needRemindUserId;
    /**
     * 保密操作的用户 id
     */
    private Long confidentialOwnerId;

    /**
     * 保密操作的用户名
     */
    private String confidentialOwnerName;

    /**
     * 保密的候选人 id
     */
    private Long talentId;

    /**
     * 候选人名称
     */
    private String fullName;

    /**
     * 候选人头像
     */
    private String photoUrl;

    /**
     * 候选人地址
     */
    private LocationDTO currentLocation;

    /**
     * 薪资币种
     */
    private String currency;

    /**
     * 薪资范围
     */
    private RangeDTO salaryRange;

    /**
     * 薪资方式
     */
    private RateUnitType payType;

    /**
     * 教育经历
     */
    private List<TalentEducationDTO> educations;

    /**
     * 工作经历
     */
    private List<TalentExperienceDTO> experiences;

    public MessageCreateWithConfidentialTalent(TalentDTOV3 talentInfo) {
        this.talentId = talentInfo.getId();
        this.tenantId = talentInfo.getTenantId();
        this.fullName = talentInfo.getFullName();
        this.photoUrl = talentInfo.getPhotoUrl();
        this.currentLocation = talentInfo.getCurrentLocation();
        this.currency = talentInfo.getCurrency();
        this.salaryRange = talentInfo.getSalaryRange();
        this.payType = talentInfo.getPayType();
        this.educations = talentInfo.getEducations();
        this.experiences = talentInfo.getExperiences();
    }
}
