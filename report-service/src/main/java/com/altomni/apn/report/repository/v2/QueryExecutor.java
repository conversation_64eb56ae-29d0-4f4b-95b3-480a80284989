package com.altomni.apn.report.repository.v2;

import cn.hutool.core.util.ReflectUtil;
import com.altomni.apn.report.config.DataSourceHolder;
import com.altomni.apn.report.domain.enumeration.DataSourceType;
import com.altomni.apn.report.util.MapToEntityUtil;
import lombok.extern.slf4j.Slf4j;
import org.hibernate.SQLQuery;
import org.hibernate.transform.Transformers;
import org.jooq.Param;
import org.jooq.Record;
import org.jooq.SelectHavingStep;
import org.jooq.conf.ParamType;
import org.springframework.stereotype.Repository;
import org.springframework.util.StopWatch;

import javax.persistence.EntityManager;
import javax.persistence.Query;
import java.lang.reflect.Method;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

@Slf4j
@Repository
public class QueryExecutor {

    private final EntityManager entityManager;

    public QueryExecutor(EntityManager entityManager) {
        this.entityManager = entityManager;
    }

    /**
     * 通用的 KPI 查询执行方法
     * @param query JOOQ 查询对象
     * @param resultClass 结果类型
     * @param methodName 方法名称（用于日志）
     * @return 查询结果列表
     */
    public  <T> List<T> executeKpiQuery(SelectHavingStep<Record> query, Class<T> resultClass, String methodName) {
        StopWatch stopWatch = new StopWatch("KPI Query Execution");
        DataSourceHolder.setCurrentDb(DataSourceType.STARROCKS);
        
        try {
            stopWatch.start("Generate SQL and parameters");
            String sql = query.getSQL(ParamType.NAMED);
            Map<String, Param<?>> params = query.getParams();
            Map<String, Object> paramMap = new HashMap<>();
            params.forEach((k, v) -> {
                paramMap.put(k, v.getValue());
            });
            stopWatch.stop();
            
            log.info("Starting KPI query execution: {}", methodName);
            log.debug("SQL: {}", sql);
            log.debug("Parameters: {}", paramMap);
            
            return getClassList(sql, paramMap, resultClass, methodName, stopWatch);
        } finally {
            DataSourceHolder.clear();
            log.info("KPI query execution completed: {} - Total time: {}ms\n{}", 
                methodName, stopWatch.getTotalTimeMillis(), stopWatch.prettyPrint());
        }
    }

    private  <T> List<T> getClassList(String sql, Map<String, Object> param, Class<T> clazz, String methodName, StopWatch stopWatch) {
        try {
            stopWatch.start("Execute database query");
            List<Map<String, Object>> mapList = doSearchDataWithNameMap(sql, param);
            stopWatch.stop();
            
            stopWatch.start("Convert to entity objects");
            List<T> result = MapToEntityUtil.convertEntity(mapList, clazz);
            stopWatch.stop();
            
            log.info("Query {} returned {} records", methodName, result.size());
            return result;
        } catch (Exception e) {
            log.error("Error occurred while executing query {}", methodName, e);
            throw e;
        }
    }

    private List<Map<String, Object>> doSearchDataWithNameMap(String queryStr, Map<String, Object> map) {
        try {
            entityManager.clear();
            Query query = entityManager.createNativeQuery(queryStr);
            Method method = ReflectUtil.getMethod(Query.class, "setParameter", String.class, Object.class);
            Optional.ofNullable(map).ifPresent(m -> m.forEach((k, v) -> ReflectUtil.invoke(query, method, k, v)));
            query.unwrap(SQLQuery.class).setResultTransformer(Transformers.ALIAS_TO_ENTITY_MAP);
            return query.getResultList();
        } catch (Exception e) {
            log.error("Error occurred while executing native SQL query: {}", queryStr, e);
            throw e;
        }
    }
}