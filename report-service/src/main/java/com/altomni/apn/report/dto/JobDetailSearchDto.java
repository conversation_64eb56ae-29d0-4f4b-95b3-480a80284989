package com.altomni.apn.report.dto;

import com.altomni.apn.common.domain.enumeration.job.JobStatus;
import com.altomni.apn.common.dto.search.SearchSortDTO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class JobDetailSearchDto {

    private SearchSortDTO sort;

    private String jobTitle;

    private Long jobId;

    private String jobCode;

    private JobStatus jobStatus;

    private Long recruitmentProcessId;

    private Long companyId;

    private String clientContact;

    private Long assignedUser;

    private String jobLocation;

    private Boolean flexibleLocation;

    private String skills;

}
