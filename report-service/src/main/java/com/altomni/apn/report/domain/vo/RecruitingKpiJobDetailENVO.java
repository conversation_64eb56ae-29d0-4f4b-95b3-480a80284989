package com.altomni.apn.report.domain.vo;

import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.altomni.apn.common.domain.enumeration.job.JobStatus;
import com.altomni.apn.common.domain.enumeration.job.JobType;
import com.altomni.apn.common.utils.CommonUtils;
import com.altomni.apn.report.domain.enumeration.JobStatusDataConverter;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.Instant;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class RecruitingKpiJobDetailENVO {


    @ExcelIgnore
    private Instant postingDate;

    @ExcelProperty(value = "Job Open Date", index = 0)
    private String openTimeFormat;

    @ExcelIgnore
    private Instant openTime;

    @ExcelProperty(value = "Job Posting Date", index = 1)
    private String postTingTimeFormat;

    @ExcelIgnore
    private Instant tenantWebsitePostingDate;

    @ExcelProperty(value = "Posting to IPG Website Date", index = 2)
    private String tenantWebsitePostingDateFormat;

    @ExcelProperty(value = "Job Title", index = 3)
    private String jobTitle;

    @ExcelProperty(value = "Job ID", index = 4)
    private Long jobId;

    @ExcelProperty(value = "Job Code", index = 5)
    private String jobCode;

    @ExcelProperty(value = "Status", index = 6, converter = JobStatusDataConverter.class)
    private JobStatus jobStatus;

    @ExcelProperty(value = "Job Openings", index = 7)
    private Long openings;

    @ExcelIgnore
    private Long recruitmentProcessId;

    @ExcelProperty(value = "Type", index = 8)
    private String jobTypeName;

    @ExcelIgnore
    private JobType jobType;

    @ExcelProperty(value = "Company", index = 9)
    private String companyName;

    @ExcelIgnore
    private Long companyId;

    @ExcelProperty(value = "Division", index = 10)
    private String division;

    @ExcelProperty(value = "Client Contact", index = 11)
    private String clientContact;

    @ExcelProperty(value = "Assigned User", index = 12)
    private String assignedUser;

    @ExcelProperty(value = "Job Desired Salary -Minimum Pay Rate", index = 13)
    private String minimumPayRate;

    @ExcelProperty(value = "Job Desired Salary -Maximum Pay Rate", index = 14)
    private String maximumPayRate;

    @ExcelProperty(value = "Minimum Bill Rate", index = 15)
    private String minimumBillRate;

    @ExcelProperty(value = "Maximum Bill Rate", index = 16)
    private String maximumBillRate;

    @ExcelProperty(value = "Rate Per", index = 17)
    private String ratePer;

    @ExcelProperty(value = "Contract Duration", index = 18)
    private String contractDuration;

    @ExcelProperty(value = "Start Date", index = 19)
    private String startDate;

    @ExcelProperty(value = "End Date", index = 20)
    private String endDate;

    @ExcelProperty(value = "Location", index = 21)
    private String jobLocation;

    @ExcelIgnore
    private Boolean flexibleLocation;

    @ExcelProperty(value = "Skills", index = 22)
    private String skills;

    @ExcelProperty(value = "Sum of Submitted to Client ", index = 23)
    private Long submitToClientNum;

    @ExcelProperty(value = "Sum of First Round ", index = 24)
    private Long firstInterviewNum;

    @ExcelProperty(value = "Sum of Second Round ", index = 25)
    private Long secondInterviewNum;

    @ExcelProperty(value = "Sum of Final Round ", index = 26)
    private Long finalInterviewNum;

    @ExcelProperty(value = "Sum of Interviewed", index = 27)
    private Long interviewNum;

    @ExcelProperty(value = "Sum of Offered", index = 28)
    private Long offerNum;

    @ExcelProperty(value = "Sum of On boarded", index = 29)
    private Long onBoardNum;

    @ExcelProperty(value = "Job Currency", index = 30)
    private String jobCurrency;

    @ExcelIgnore
    private String ecName;

    @ExcelProperty(value = "Job Cooperation Status", index = 31)
    private String jobCooperationStatus;

    @ExcelProperty(value = "Contract Notes", index = 32)
    private Long contractNotes;

    public String getJobCurrency() {
        if (StrUtil.isBlank(jobCurrency)) {
            return "";
        }
        return ecName + " / " + jobCurrency;
    }

    public String getSkills() {
        return skills;
    }

    public String getClientContact() {
        if (StrUtil.isBlank(clientContact)) {
            return "";
        }
        return Arrays.stream(clientContact.split(","))
                .map(StrUtil::trim)
                .filter(StrUtil::isNotBlank)
                .filter(client -> client.split("-").length > 1)
                .map(client -> client.split("-")[1])
                .collect(Collectors.joining(","));
    }

    public String getAssignedUser() {
        if (StrUtil.isBlank(assignedUser)) {
            return "";
        }
        return Arrays.stream(assignedUser.split(","))
                .map(StrUtil::trim)
                .filter(StrUtil::isNotBlank)
                .filter(client -> client.split("-").length > 1)
                .map(client -> {
                    List<String> nameList = StrUtil.split(client, '-', 2);
                    String name = nameList.get(1);
                    String[] names = name.split(" ");
                    return names.length > 1 ? CommonUtils.formatFullName(names[0], names[1]) : name;
                }).collect(Collectors.joining(","));
    }

    public String getJobLocation() {
        return jobLocation;
    }

    public JobStatus getJobStatus() {
        if (jobType == JobType.PAY_ROLL) {
            return null;
        }
        return jobStatus;
    }
}
