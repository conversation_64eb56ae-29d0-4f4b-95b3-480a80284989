package com.altomni.apn.common.utils;

import com.altomni.apn.common.dto.message.MessageCreateWithConfidentialTalent;
import com.altomni.apn.common.dto.talent.TalentEducationDTO;
import com.altomni.apn.common.dto.talent.TalentExperienceDTO;
import org.junit.jupiter.api.Test;

import java.io.FileOutputStream;
import java.io.IOException;
import java.time.LocalDate;
import java.util.Arrays;

public class ChartUtilTest {

    @Test
    public void testGenerateConfidentialTalentCard() throws IOException {
        // 创建测试数据
        MessageCreateWithConfidentialTalent messageCreateDTO = new MessageCreateWithConfidentialTalent();
        messageCreateDTO.setTalentId(123456L);
        messageCreateDTO.setFullName("张三");
        messageCreateDTO.setConfidentialOwnerName("李四");
        
        // 创建工作经验
        TalentExperienceDTO exp1 = new TalentExperienceDTO();
        exp1.setCompanyName("阿里巴巴集团");
        exp1.setTitle("高级软件工程师");
        exp1.setStartDate(LocalDate.of(2020, 1, 1));
        exp1.setEndDate(LocalDate.of(2023, 12, 31));
        exp1.setCurrent(false);
        
        TalentExperienceDTO exp2 = new TalentExperienceDTO();
        exp2.setCompanyName("腾讯科技");
        exp2.setTitle("软件工程师");
        exp2.setStartDate(LocalDate.of(2018, 6, 1));
        exp2.setEndDate(LocalDate.of(2019, 12, 31));
        exp2.setCurrent(false);
        
        messageCreateDTO.setExperiences(Arrays.asList(exp1, exp2));
        
        // 创建教育经历
        TalentEducationDTO edu1 = new TalentEducationDTO();
        edu1.setCollegeName("清华大学");
        edu1.setDegreeName("硕士");
        edu1.setMajorName("计算机科学与技术");
        edu1.setStartDate(LocalDate.of(2016, 9, 1));
        edu1.setEndDate(LocalDate.of(2018, 6, 30));
        edu1.setCurrent(false);
        
        TalentEducationDTO edu2 = new TalentEducationDTO();
        edu2.setCollegeName("北京大学");
        edu2.setDegreeName("学士");
        edu2.setMajorName("软件工程");
        edu2.setStartDate(LocalDate.of(2012, 9, 1));
        edu2.setEndDate(LocalDate.of(2016, 6, 30));
        edu2.setCurrent(false);
        
        messageCreateDTO.setEducations(Arrays.asList(edu1, edu2));
        
        // 生成图片
        byte[] imageBytes = ChartUtil.generateConfidentialTalentCard(messageCreateDTO);
        
        // 保存到文件用于查看
        try (FileOutputStream fos = new FileOutputStream("test_confidential_talent_card.png")) {
            fos.write(imageBytes);
        }
        
        System.out.println("Generated confidential talent card image: " + imageBytes.length + " bytes");
        System.out.println("Image saved as: test_confidential_talent_card.png");
    }
}
