package com.altomni.apn.report.domain.vo;

import com.altomni.apn.common.aop.confidential.AttachConfidentialTalent;
import com.altomni.apn.common.domain.enumeration.application.NodeType;
import com.altomni.apn.common.domain.enumeration.application.NodeTypeConverter;
import com.altomni.apn.common.domain.enumeration.job.JobStatus;
import com.altomni.apn.common.domain.enumeration.job.JobStatusConverter;
import com.altomni.apn.common.dto.application.dashboard.MyCandidateStatusFilter;
import com.altomni.apn.common.dto.application.dashboard.MyCandidateStatusFilterConverter;
import com.altomni.apn.common.dto.talent.ConfidentialInfoDto;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Convert;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Transient;
import java.io.Serializable;
import java.time.Instant;

@Data
@Entity
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class RecruitingKpiApplicationNoteDetailVO implements Serializable, AttachConfidentialTalent {

    @Id
    private String virtualId;

    private String talentRecruitmentProcessId;

    private Long id;

    private String fullName;

    private Long talentId;

    private String jobTitle;

    private Long jobId;

    @Convert(converter = JobStatusConverter.class)
    private JobStatus jobStatus;

    @Convert(converter = MyCandidateStatusFilterConverter.class)
    private MyCandidateStatusFilter workflowStatus;

    private String note;

    private Long createdBy;

    private Long lastModifiedBy;

    private Instant createdDate;

    private Instant lastModifiedDate;

    @Transient
    private boolean isPrivateJob;

    private Long pteamId;

    @Convert(converter = NodeTypeConverter.class)
    private NodeType nodeType;

    @Transient
    private Boolean confidentialTalentViewAble;

    @Transient
    private ConfidentialInfoDto confidentialInfo;

    @Override
    public void encrypt() {
        this.talentRecruitmentProcessId = null;
        this.id = null;
        this.fullName = null;
        this.talentId = null;
        this.jobTitle = null;
        this.jobId = null;
        this.jobStatus = null;
        this.workflowStatus = null;
        this.note = null;
        this.createdBy = null;
        this.lastModifiedBy = null;
        this.createdDate = null;
        this.lastModifiedDate = null;
        this.isPrivateJob = false;
        this.pteamId = null;
        this.nodeType = null;
    }
}
