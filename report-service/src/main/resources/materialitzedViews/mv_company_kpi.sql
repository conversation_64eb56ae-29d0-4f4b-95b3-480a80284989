-- 创建公司、升级为正式客户的物化视图
CREATE
MATERIALIZED VIEW IF NOT EXISTS ods_apn.mv_company_kpi
       DISTRIBUTED BY HASH (`user_id`)
REFRESH
       ASYNC EVERY (INTERVAL 10 MINUTE)
ORDER BY (add_date, event_date)
AS
SELECT *
FROM (
-- 创建公司
         (SELECT ac.tenant_id                           AS tenant_id,
                 pt.id                                  AS team_id,
                 pt.name                                AS team_name,
                 u.id                                   AS user_id,
                 CONCAT(u.first_name, ' ', u.last_name) AS user_name,
                 u.activated                            AS user_activated,
                 ac.created_date                        AS add_date,
                 ac.created_date                        AS event_date,
                 BITMAP_UNION(TO_BITMAP(ac.id))         AS created_company_count,
                 BITMAP_EMPTY()                         AS upgrade_company_count
          FROM ods_crm.account_company AS ac
                   INNER JOIN ods_crm.business_flow_administrator AS bfa ON ac.id = bfa.account_company_id
                   INNER JOIN ods_apn.user AS u ON bfa.user_id = u.id
                   INNER JOIN ods_apn.permission_user_team AS put ON u.id = put.user_id AND put.is_primary = 1
                   INNER JOIN ods_apn.permission_team AS pt ON put.team_id = pt.id
          GROUP BY ac.tenant_id, pt.id, pt.name, u.id, u.first_name, u.last_name, u.activated, ac.created_date)
         UNION ALL
-- 升级正式客户
         (SELECT c.tenant_id                            AS tenant_id,
                 pt.id                                  AS team_id,
                 pt.name                                AS team_name,
                 u.id                                   AS user_id,
                 CONCAT(u.first_name, ' ', u.last_name) AS user_name,
                 u.activated                            AS user_activated,
                 c.request_date                         AS add_date,
                 c.request_date                         AS event_date,
                 BITMAP_EMPTY()                         AS created_company_count,
                 BITMAP_UNION(TO_BITMAP(c.id))          AS upgrade_company_count
          FROM company AS c
                   INNER JOIN business_flow_administrator AS bfa ON c.id = bfa.company_id
                   INNER JOIN user AS u ON bfa.user_id = u.id
                   INNER JOIN permission_user_team AS put ON u.id = put.user_id AND put.is_primary = 1
                   INNER JOIN permission_team AS pt ON put.team_id = pt.id
          WHERE c.active = 30
          GROUP BY c.tenant_id, pt.id, pt.name, u.id, u.first_name, u.last_name, u.activated, c.request_date))
         AS final_table;