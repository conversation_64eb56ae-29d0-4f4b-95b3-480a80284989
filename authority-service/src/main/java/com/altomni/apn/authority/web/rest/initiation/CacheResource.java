package com.altomni.apn.authority.web.rest.initiation;

import com.altomni.apn.authority.service.cache.CachePermissionInitiation;
import com.altomni.apn.common.datapermission.rule.team.TeamDataPermissionRule;
import com.altomni.apn.common.dto.permission.TeamDataPermissionRespDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * REST controller for initiating cache.
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/api/v3/cache")
public class CacheResource {

    @Resource
    private CachePermissionInitiation cachePermissionInitiation;

    @GetMapping("/initiate-data-permission/tenant/{tenantId}/user/{userId}")
    public ResponseEntity<TeamDataPermissionRespDTO> initiateDataPermissionByUserId(@PathVariable("tenantId") Long tenantId, @PathVariable("userId") Long userId) {
        log.debug("REST request to initiate data permission by user id: {}, tenant id: {}", userId, tenantId);
        return ResponseEntity.ok(cachePermissionInitiation.getDeptDataPermissionFromCacheOrDB(userId, tenantId));
    }

    @GetMapping("/initiate-candidate-pipeline-management-data-permission/tenant/{tenantId}/user/{userId}")
    public ResponseEntity<TeamDataPermissionRespDTO> initiateCandidatePipelineManagementDataPermissionByUserId(@PathVariable("tenantId") Long tenantId, @PathVariable("userId") Long userId) {
        log.debug("REST request to initiate candidate pipeline management data permission by user id: {}, tenant id: {}", userId, tenantId);
        return ResponseEntity.ok(cachePermissionInitiation.getCandidatePipelineManagementDataPermissionFromCacheOrDB(userId, tenantId));
    }

    @GetMapping("/initiate-client-contact-data-permission/tenant/{tenantId}/user/{userId}")
    public ResponseEntity<TeamDataPermissionRespDTO> initiateClientContactDataPermissionByUserId(@PathVariable("tenantId") Long tenantId, @PathVariable("userId") Long userId) {
        log.debug("REST request to initiate client contact data permission by user id: {}, tenant id: {}", userId, tenantId);
        return ResponseEntity.ok(cachePermissionInitiation.getClientContactDataPermissionFromCacheOrDB(userId, tenantId));
    }

    @GetMapping("/initiate-report-data-permission/tenant/{tenantId}/user/{userId}")
    public ResponseEntity<TeamDataPermissionRespDTO> initiateReportDataPermissionByUserId(@PathVariable("tenantId") Long tenantId, @PathVariable("userId") Long userId) {
        log.debug("REST request to initiate report data permission by user id: {}, tenant id: {}", userId, tenantId);
        return ResponseEntity.ok(cachePermissionInitiation.getReportDataPermissionFromCacheOrDB(userId, tenantId));
    }

    @GetMapping("/initiate-home-and-calendar-data-permission/tenant/{tenantId}/user/{userId}")
    public ResponseEntity<TeamDataPermissionRespDTO> initiateHomeAndCalendarDataPermissionByUserId(@PathVariable("tenantId") Long tenantId, @PathVariable("userId") Long userId) {
        log.debug("REST request to initiate report data permission by user id: {}, tenant id: {}", userId, tenantId);
        return ResponseEntity.ok(cachePermissionInitiation.getHomeAndCalendarDataPermissionFromCacheOrDB(userId, tenantId));
    }

    @GetMapping("initiate-china-invoicing-data-permission/tenant/{tenantId}/user/{userId}")
    public ResponseEntity<TeamDataPermissionRespDTO> initiateChinaInvoicingDataPermissionByUserId(@PathVariable("tenantId") Long tenantId, @PathVariable("userId") Long userId) {
        log.debug("REST request to initiate report data permission by user id: {}, tenant id: {}", userId, tenantId);
        return ResponseEntity.ok(cachePermissionInitiation.initiateChinaInvoicingDataPermissionByUserId(userId, tenantId));
    }

    @GetMapping("/initiate-confidential-talent-data-permission/tenant/{tenantId}/user/{userId}")
    public ResponseEntity<TeamDataPermissionRespDTO> initiateConfidentialTalentDataPermissionByUserId(@PathVariable("tenantId") Long tenantId, @PathVariable("userId") Long userId) {
        log.debug("REST request to initiate confidential talent data permission by user id: {}, tenant id: {}", userId, tenantId);
        return ResponseEntity.ok(cachePermissionInitiation.getConfidentialTalentDataPermissionFromCacheOrDB(userId, tenantId));
    }

    @GetMapping("/initiate-permission-rules-by-tenant-id/{tenantId}")
    ResponseEntity<List<TeamDataPermissionRule>> initiatePermissionRulesByTenantId(@PathVariable("tenantId") Long tenantId){
        log.debug("REST request to initiate permission rules by tenant id: {}", tenantId);
        List<TeamDataPermissionRule> dataPermissionRuleList = cachePermissionInitiation.getPermissionRulesByTenantIdFromCacheOrDB(tenantId);
        log.debug("dataPermissionRuleList.size() = " + dataPermissionRuleList.size());
        return ResponseEntity.ok(dataPermissionRuleList);
    }

}
