package com.altomni.apn.report.service.recruiting;

import com.altomni.apn.common.dto.recruiting.RecruitingKpiReportSearchDto;
import com.altomni.apn.common.vo.recruiting.RecruitingKpiByCompanyVO;
import com.altomni.apn.common.vo.recruiting.RecruitingKpiByUserVO;
import com.altomni.apn.report.domain.vo.*;
import com.altomni.apn.report.dto.*;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

public interface RecruitingKpiService {

    List<RecruitingKpiByUserVO> searchRecruitingKpiReportByUser(RecruitingKpiReportSearchDto searchDto);

    List<RecruitingKpiByUserVO> searchRecruitingKpiReportByUserForE5(RecruitingKpiReportSearchDto searchDto);

    List<RecruitingKpiByCompanyVO> searchRecruitingKpiReportByCompany(RecruitingKpiReportSearchDto searchDto);

}
