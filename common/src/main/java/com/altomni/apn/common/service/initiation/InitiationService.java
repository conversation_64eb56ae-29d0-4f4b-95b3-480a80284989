package com.altomni.apn.common.service.initiation;

import com.altomni.apn.common.datapermission.rule.team.TeamDataPermissionRule;
import com.altomni.apn.common.dto.permission.TeamDataPermissionRespDTO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;

import java.util.List;

/**
 * <AUTHOR>
 */
@Component
@FeignClient(value = "authority-service")
public interface InitiationService {

    @GetMapping(path = "/authority/api/v3/cache/initiate-data-permission/tenant/{tenantId}/user/{userId}")
    ResponseEntity<TeamDataPermissionRespDTO> initiateDataPermissionByUserId(@PathVariable("tenantId") Long tenantId, @PathVariable("userId") Long userId);

    @GetMapping(path = "/authority/api/v3/cache/initiate-client-contact-data-permission/tenant/{tenantId}/user/{userId}")
    ResponseEntity<TeamDataPermissionRespDTO> initiateClientContactDataPermissionByUserId(@PathVariable("tenantId") Long tenantId, @PathVariable("userId") Long userId);

    @GetMapping(path = "/authority/api/v3/cache/initiate-candidate-pipeline-management-data-permission/tenant/{tenantId}/user/{userId}")
    ResponseEntity<TeamDataPermissionRespDTO> initiateCandidatePipelineManagementDataPermissionByUserId(@PathVariable("tenantId") Long tenantId, @PathVariable("userId") Long userId);

    @GetMapping(path = "/authority/api/v3/cache/initiate-report-data-permission/tenant/{tenantId}/user/{userId}")
    ResponseEntity<TeamDataPermissionRespDTO> initiateReportDataPermissionByUserId(@PathVariable("tenantId") Long tenantId, @PathVariable("userId") Long userId);

    @GetMapping("/authority/api/v3/cache/initiate-home-and-calendar-data-permission/tenant/{tenantId}/user/{userId}")
    ResponseEntity<TeamDataPermissionRespDTO> initiateHomeAndCalendarDataPermissionByUserId(@PathVariable("tenantId") Long tenantId, @PathVariable("userId") Long userId);

    @GetMapping(path = "/authority/api/v3/cache/initiate-permission-rules-by-tenant-id/{tenantId}")
    ResponseEntity<List<TeamDataPermissionRule>> initiatePermissionRulesByTenantId(@PathVariable("tenantId") Long tenantId);

    @GetMapping("/authority/api/v3/cache/initiate-china-invoicing-data-permission/tenant/{tenantId}/user/{userId}")
    ResponseEntity<TeamDataPermissionRespDTO> initiateChinaInvoicingDataPermissionByUserId(@PathVariable("tenantId") Long tenantId, @PathVariable("userId") Long userId);

    @GetMapping("/authority/api/v3/cache/initiate-confidential-talent-data-permission/tenant/{tenantId}/user/{userId}")
    ResponseEntity<TeamDataPermissionRespDTO> initiateConfidentialTalentDataPermissionByUserId(@PathVariable("tenantId") Long tenantId, @PathVariable("userId") Long userId);

}
