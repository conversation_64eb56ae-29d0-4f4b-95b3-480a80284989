package com.altomni.apn.user.service.permission.impl;

import com.altomni.apn.common.config.CommonApiMultilingualConfig;
import com.altomni.apn.common.domain.user.User;
import com.altomni.apn.common.dto.permission.TeamDataPermissionRespDTO;
import com.altomni.apn.common.enumeration.enums.UserAPIMultilingualEnum;
import com.altomni.apn.common.enumeration.permission.DataScope;
import com.altomni.apn.common.enumeration.permission.Module;
import com.altomni.apn.common.errors.CustomParameterizedException;
import com.altomni.apn.common.service.initiation.InitiationService;
import com.altomni.apn.common.utils.CommonUtils;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.user.config.env.UserApiPromptProperties;
import com.altomni.apn.user.domain.permission.PermissionExtraUserTeam;
import com.altomni.apn.user.repository.permission.PermissionExtraUserTeamRepository;
import com.altomni.apn.user.repository.permission.PermissionUserRoleRepository;
import com.altomni.apn.user.repository.user.UserRepository;
import com.altomni.apn.user.service.cache.CachePermissionWriteOnly;
import com.altomni.apn.user.service.permission.PermissionExtraUserTeamService;
import com.altomni.apn.user.service.permission.PermissionUserTeamService;
import com.altomni.apn.user.web.rest.vm.permission.PermissionUserTeamPermissionVM;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.SetUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

@Slf4j
@Service
@Transactional
public class PermissionExtraUserTeamServiceImpl implements PermissionExtraUserTeamService {

    @Resource
    private PermissionExtraUserTeamRepository permissionExtraUserTeamRepository;

    @Resource
    private PermissionUserRoleRepository permissionUserRoleRepository;

    @Resource
    private CachePermissionWriteOnly cachePermissionWriteOnly;

    @Resource
    private UserRepository userRepository;

    @Resource
    CommonApiMultilingualConfig commonApiMultilingualConfig;

    @Resource
    UserApiPromptProperties userApiPromptProperties;


    @Resource
    private PermissionUserTeamService permissionUserTeamService;

    @Resource
    private InitiationService initiationService;


    @Override
    public void saveUserTeamPermission(Long userId, PermissionUserTeamPermissionVM permissionUserTeamPermissionVM) {
        permissionExtraUserTeamRepository.deleteAllByUserId(userId);
        this.saveUserTeamPermissionByModule(userId, permissionUserTeamPermissionVM.getJobPermission(), Module.JOB);
        this.saveUserTeamPermissionByModule(userId, permissionUserTeamPermissionVM.getClientContactPermission(), Module.CLIENT_CONTACT);
        this.saveUserTeamPermissionByModule(userId, permissionUserTeamPermissionVM.getReportPermission(), Module.REPORT);
        this.saveUserTeamPermissionByModule(userId, permissionUserTeamPermissionVM.getHomeAndCalendarPermission(), Module.HOME_AND_CALENDAR);
        this.saveUserTeamPermissionByModule(userId, permissionUserTeamPermissionVM.getCandidatePipelineManagementPermission(), Module.CANDIDATE_PIPELINE_MANAGEMENT);
        this.saveUserTeamPermissionByModule(userId, permissionUserTeamPermissionVM.getChinaInvoicingModulePermission(), Module.CHINA_INVOICING);
        this.saveUserTeamPermissionByModule(userId, permissionUserTeamPermissionVM.getConfidentialTalentPermission(), Module.CONFIDENTIAL_TALENT);
        permissionUserRoleRepository.updateUserDataScopeById(permissionUserTeamPermissionVM.getJobPermission().getDataScope(),
                permissionUserTeamPermissionVM.getClientContactPermission().getDataScope(),
                permissionUserTeamPermissionVM.getReportPermission().getDataScope(),
                permissionUserTeamPermissionVM.getHomeAndCalendarPermission().getDataScope(),
                permissionUserTeamPermissionVM.getCandidatePipelineManagementPermission().getDataScope(),
                permissionUserTeamPermissionVM.getChinaInvoicingModulePermission().getDataScope(),
                permissionUserTeamPermissionVM.getConfidentialTalentPermission().getDataScope(),
                userId);
        cachePermissionWriteOnly.deleteDataPermissionCacheByUserId(userId);
        cachePermissionWriteOnly.deleteClientContactDataPermissionCacheByUserId(userId);
        cachePermissionWriteOnly.deleteReportDataPermissionCacheByUserId(userId);
        cachePermissionWriteOnly.deleteHomeAndCalendarDataPermissionCacheByUserId(userId);
        cachePermissionWriteOnly.deleteCandidatePipelineManagementPermissionCacheByUserId(userId);
        cachePermissionWriteOnly.deleteChinaInvoicingDataPermissionCacheByUserId(userId);
        cachePermissionWriteOnly.deleteConfidentialTalentDataPermissionCacheByUserId(userId);
    }

    private void saveUserTeamPermissionByModule(Long userId, PermissionUserTeamPermissionVM.PermissionDetail permissionDetail, Module module){
        Integer dataScope = permissionDetail.getDataScope();
        Set<Long> teamIds = permissionDetail.getTeamIds();
        boolean modifiable = permissionDetail.isModifiable();
        if (dataScope.equals(DataScope.PERMISSION_EXTRA_TEAM.toDbValue()) && CollectionUtils.isNotEmpty(teamIds)){
            permissionExtraUserTeamRepository.saveAll(teamIds.stream().map(teamId -> {
                return new PermissionExtraUserTeam(null, userId, module, teamId, SecurityUtils.getTenantId(), modifiable);
            }).collect(Collectors.toList()));
        }
    }

    @Override
    public PermissionUserTeamPermissionVM findDataPermissionByUserId(Long userId) {
        if (userId < 0){
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(UserAPIMultilingualEnum.PERMISSION_FINDDATAPERMISSIONBYUSERID_INVALIDUSER.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),userApiPromptProperties.getUserService()));
        }

        User user = userRepository.findById(userId).orElseThrow(() -> new CustomParameterizedException("Cannot find this user!"));
        PermissionUserTeamPermissionVM vm = new PermissionUserTeamPermissionVM();
        vm.setJobPermission(this.getPermissionDetail(userId, user.getDataScope(), Module.JOB));
        vm.setClientContactPermission(this.getPermissionDetail(userId, user.getClientContactDataScope(), Module.CLIENT_CONTACT));
        vm.setReportPermission(this.getPermissionDetail(userId, user.getReportDataScope(), Module.REPORT));
        vm.setHomeAndCalendarPermission(this.getPermissionDetail(userId, user.getHomeAndCalendarDataScope(), Module.HOME_AND_CALENDAR));
        vm.setCandidatePipelineManagementPermission(this.getPermissionDetail(userId, user.getCandidatePipelineManagementDataScope(), Module.CANDIDATE_PIPELINE_MANAGEMENT));
        vm.setChinaInvoicingModulePermission(this.getPermissionDetail(userId,user.getChinaInvoicingDataScope(),Module.CHINA_INVOICING));
        vm.setConfidentialTalentPermission(this.getPermissionDetail(userId, user.getConfidentialTalentDataScope(), Module.CONFIDENTIAL_TALENT));
        return vm;
    }

    private PermissionUserTeamPermissionVM.PermissionDetail getPermissionDetail(Long userId, Integer dataScope, Module module){
        PermissionUserTeamPermissionVM.PermissionDetail permissionDetail = new PermissionUserTeamPermissionVM.PermissionDetail();
        if (Objects.isNull(dataScope)){
            dataScope = DataScope.PERMISSION_SELF.toDbValue();
        }
        permissionDetail.setUserId(userId);
        permissionDetail.setDataScope(dataScope);
        if (dataScope.equals(DataScope.PERMISSION_EXTRA_TEAM.toDbValue())){
            permissionDetail.setTeamIds(permissionExtraUserTeamRepository.findAllByUserIdAndModule(userId, module).stream().map(permissionExtraUserTeam -> {
                permissionDetail.setModifiable(permissionExtraUserTeam.getWritable());
                return permissionExtraUserTeam.getTeamId();
            }).collect(Collectors.toSet()));
        }
        return permissionDetail;
    }

    @Override
    public PermissionUserTeamPermissionVM.PermissionDetail findAllClientContactDataPermissionByUserIdAndTenantId(Long tenantId, Long userId) {
        if (userId < 0){
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(UserAPIMultilingualEnum.PERMISSION_FINDDATAPERMISSIONBYUSERID_INVALIDUSER.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),userApiPromptProperties.getUserService()));
        }

//        User user = userRepository.findById(userId).orElseThrow(() -> new CustomParameterizedException("Cannot find this user!"));
        PermissionUserTeamPermissionVM.PermissionDetail vm = new PermissionUserTeamPermissionVM.PermissionDetail();
        TeamDataPermissionRespDTO dataPermissionRespDTO = initiationService.initiateClientContactDataPermissionByUserId(tenantId, userId).getBody();
        Set<Long> readableTeamIds = dataPermissionRespDTO.getReadableTeamIds();
//        List<PermissionExtraUserTeam> permissionExtraUserTeams = permissionExtraUserTeamRepository.findAllByUserIdAndModule(userId, module);
//        List<UserInfoWithPermission> userInfoWithPermissionByUserIdIn = userRepository.findUserInfoWithPermissionByUserIdIn(List.of(userId));
//        List<UserInfoWithPermission> secondUserInfoWithPermission = userInfoWithPermissionByUserIdIn.stream().filter(f -> Boolean.FALSE.equals(f.getIsPrimary())).collect(Collectors.toList());
//        List<UserRoleWithPermission> userRoleWithPermissionByUserId = userRepository.findUserRoleWithPermissionByUserId(userId);
//        List<Long> roleIdList = userRoleWithPermissionByUserId.stream().map(UserRoleWithPermission::getRoleId).collect(Collectors.toList());
//        Map<Long, List<PermissionExtraRoleTeam>> roleId2ExtraTeamIdMap = permissionExtraRoleTeamRepository.findAllByRoleIdIn(roleIdList).stream().collect(Collectors.groupingBy(PermissionExtraRoleTeam::getRoleId));
        vm.setUserId(userId);
//        Optional<UserInfoWithPermission> first = userInfoWithPermissionByUserIdIn.stream().filter(f -> Boolean.TRUE.equals(f.getIsPrimary())).findFirst();
//        first.ifPresent(userInfoWithPermission -> vm.setPrimaryTeamId(userInfoWithPermission.getTeamId()));
        vm.setPrimaryTeamId(permissionUserTeamService.findPrimaryTeamByUserId(userId).getTeamId());
//        vm.setDataScope(UserServiceImpl.getMaxDataScope(secondUserInfoWithPermission, dataScope, userRoleWithPermissionByUserId, teamIds, permissionExtraUserTeams, roleId2ExtraTeamIdMap));
        if (Objects.isNull(vm.getPrimaryTeamId())){
            vm.setTeamIds(readableTeamIds);
        }else {
            vm.setTeamIds(SetUtils.difference(readableTeamIds, Set.of(vm.getPrimaryTeamId())));
        }

        vm.setDataScope(this.getDataScope(dataPermissionRespDTO));
        return vm;
    }

    private Integer getDataScope(TeamDataPermissionRespDTO dataPermissionRespDTO){
        if (dataPermissionRespDTO.getAll()){
            return DataScope.PERMISSION_ALL.toDbValue();
        }
        if (dataPermissionRespDTO.getSelf()){
            return DataScope.PERMISSION_SELF.toDbValue();
        }
        if (dataPermissionRespDTO.getReadableTeamIds().size() == 1){
            return DataScope.PERMISSION_TEAM.toDbValue();
        }
        if (dataPermissionRespDTO.getReadableTeamIds().size() > 1){
            return DataScope.PERMISSION_EXTRA_TEAM.toDbValue();
        }
        return DataScope.PERMISSION_NO.toDbValue();
    }
}
