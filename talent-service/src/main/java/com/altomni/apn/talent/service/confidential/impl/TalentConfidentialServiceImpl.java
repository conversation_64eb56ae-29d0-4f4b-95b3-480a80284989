package com.altomni.apn.talent.service.confidential.impl;

import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.altomni.apn.common.config.CommonApiMultilingualConfig;
import com.altomni.apn.common.config.audit.AuditUserHolder;
import com.altomni.apn.common.domain.AbstractAuditingEntity;
import com.altomni.apn.common.domain.dict.EnumCurrency;
import com.altomni.apn.common.domain.dict.EnumDegree;
import com.altomni.apn.common.domain.enumeration.RateUnitType;
import com.altomni.apn.common.domain.enumeration.application.NodeType;
import com.altomni.apn.common.domain.enumeration.talent.TalentOwnershipType;
import com.altomni.apn.common.domain.enumeration.talent.TalentOwnershipUserRole;
import com.altomni.apn.common.domain.talent.TalentOwnership;
import com.altomni.apn.common.domain.talent.TalentV3;
import com.altomni.apn.common.dto.application.talentrecruitmentprocess.TalentRecruitmentProcessVO;
import com.altomni.apn.common.dto.message.MessageCreateWithConfidentialTalent;
import com.altomni.apn.common.dto.permission.TeamDataPermissionRespDTO;
import com.altomni.apn.common.dto.talent.ConfidentialInfoDto;
import com.altomni.apn.common.dto.talent.TalentDTOV3;
import com.altomni.apn.common.dto.talent.TalentEducationDTO;
import com.altomni.apn.common.dto.talent.TalentAutoDeclassifyDto;
import com.altomni.apn.common.dto.user.UserBriefDTO;
import com.altomni.apn.common.enumeration.enums.TalentAPIMultilingualEnum;
import com.altomni.apn.common.enumeration.permission.Module;
import com.altomni.apn.common.errors.CustomParameterizedException;
import com.altomni.apn.common.errors.ForbiddenException;
import com.altomni.apn.common.errors.NotFoundException;
import com.altomni.apn.common.service.cache.EnumCommonService;
import com.altomni.apn.common.service.initiation.InitiationService;
import com.altomni.apn.common.utils.CommonUtils;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.talent.config.env.TalentApiPromptProperties;
import com.altomni.apn.talent.domain.confidential.ConfidentialRule;
import com.altomni.apn.talent.domain.confidential.ConfidentialTalent;
import com.altomni.apn.talent.domain.confidential.DeclassificationRules;
import com.altomni.apn.talent.domain.confidential.TalentConfidentialCondition;
import com.altomni.apn.common.domain.enumeration.talent.TalentDeclassifyType;
import com.altomni.apn.talent.repository.confidential.ConfidentialRuleRepository;
import com.altomni.apn.talent.repository.confidential.ConfidentialTalentRepository;
import com.altomni.apn.talent.repository.talent.TalentOwnershipRepository;
import com.altomni.apn.talent.repository.talent.TalentRepository;
import com.altomni.apn.talent.service.UserService;
import com.altomni.apn.talent.service.application.ApplicationService;
import com.altomni.apn.talent.service.confidential.TalentConfidentialService;
import com.altomni.apn.talent.service.dto.confidential.ConfidentialRuleDto;
import com.altomni.apn.talent.service.talent.TalentCrmSyncService;
import com.altomni.apn.talent.service.talent.TalentService;
import com.altomni.apn.talent.service.talent.TalentSyncService;
import com.altomni.apn.talent.service.xxljob.XxlJobService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.data.util.Pair;
import org.springframework.security.core.context.SecurityContext;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.DefaultTransactionDefinition;

import javax.validation.constraints.NotNull;
import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.function.Supplier;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class TalentConfidentialServiceImpl implements TalentConfidentialService {

    private final EnumCommonService enumCommonService;
    private final ConfidentialRuleRepository confidentialRuleRepository;
    private final TalentRepository talentRepository;
    private final ConfidentialTalentRepository confidentialTalentRepository;
    private final ApplicationService applicationService;
    private final TalentOwnershipRepository talentOwnershipRepository;
    private final XxlJobService xxlJobService;
    private final UserService userService;
    private final CommonApiMultilingualConfig commonApiMultilingualConfig;
    private final TalentApiPromptProperties talentApiPromptProperties;
    private final TalentSyncService talentSyncService;
    private final TalentCrmSyncService talentCrmSyncService;
    private final InitiationService initiationService;
    private final TalentService talentService;
    private final PlatformTransactionManager transactionManager;

    @Override
    @Transactional
    public ConfidentialRuleDto createRule(ConfidentialRuleDto dto) {
        dto.validate();

        ConfidentialRule newRule = toRuleEntity(dto);
        newRule.setId(null);
        newRule.setTenantId(SecurityUtils.getTenantId());
        // 新增规则，需要全局保存才能生效
        newRule.setEnabled(false);

        checkSameRule(newRule);

        confidentialRuleRepository.save(newRule);
        return toRuleDto(newRule);
    }

    @Override
    public List<ConfidentialRuleDto> listRules() {
        List<ConfidentialRule> allRule = confidentialRuleRepository.findAllByTenantId(SecurityUtils.getTenantId());
        return allRule.stream().map(this::toRuleDto).toList();
    }



    @Override
    @Transactional
    public ConfidentialRuleDto updateRule(Long id, ConfidentialRuleDto dto) {
        dto.validate();

        //如果更新规则后，不再符合保密规则的候选人，自动解除保密状态, 清除之前保密的定时任务
        Set<Long> willDeclassifyTalents = effectOnUpdate(id, dto);

        ConfidentialRule confidentialRule = confidentialRuleRepository.findByIdAndTenantId(id, SecurityUtils.getTenantId())
                .orElseThrow(() -> new NotFoundException("Rule not found"));
        confidentialRule.fillWithDto(dto);
        // 编辑规则，立即生效
        confidentialRule.setEnabled(true);

        checkSameRule(confidentialRule);
        confidentialRuleRepository.save(confidentialRule);

        if (!willDeclassifyTalents.isEmpty()) {
            List<ConfidentialTalent> confidentialTalents = confidentialTalentRepository.findAllByTalentIdInAndActive(willDeclassifyTalents, true);
            confidentialTalents.forEach(ct -> ct.inactive(TalentDeclassifyType.RESUME_NOMATCH));
            confidentialTalentRepository.saveAll(confidentialTalents);
            xxlJobService.deleteTalentDeclassifyJob(willDeclassifyTalents);
            // 重新同步候选人到 ES
            talentSyncService.syncTalentsToMQ(willDeclassifyTalents, 1);
        }

        // 可能修改了规则的保密时长，重新计算预计解除保密时间
        List<ConfidentialTalent> endTimeChangedTalent = confidentialTalentRepository.findAllByRuleIdAndActive(id, true).stream()
                .filter(ct -> ct.reComputeDeclassifyTime(confidentialRule))
                .toList();
        if (!endTimeChangedTalent.isEmpty()) {
            confidentialTalentRepository.saveAll(endTimeChangedTalent);
            endTimeChangedTalent.forEach(ct -> xxlJobService.addTalentDeclassifyJob(ct, TalentDeclassifyType.MATURITY));
        }

        return toRuleDto(confidentialRule);
    }

    @Override
    @Transactional
    public void deleteRule(Long id) {
        ConfidentialRule confidentialRule = confidentialRuleRepository.findByIdAndTenantId(id, SecurityUtils.getTenantId())
                .orElseThrow(() -> new NotFoundException("Rule not found"));
        //删除规则后，之前保密的候选人，自动解除保密状态, 清除之前保密的定时任务
        List<ConfidentialTalent> confidentialTalents = confidentialTalentRepository.findAllByRuleIdAndActive(id, true);
        confidentialRuleRepository.delete(confidentialRule);
        if (confidentialTalents.isEmpty()) {
            return;
        }
        confidentialTalents.forEach(ct -> ct.inactive(TalentDeclassifyType.RULE_DELETE));
        confidentialTalentRepository.saveAllAndFlush(confidentialTalents);
        // 取消定时任务
        List<Long> talentIds = confidentialTalents.stream().map(ConfidentialTalent::getTalentId).toList();
        xxlJobService.deleteTalentDeclassifyJob(talentIds);
        // 重新同步候选人到 ES
        talentSyncService.syncTalentsToMQ(talentIds, 1);
    }

    @Override
    @Transactional
    public void activeAllRules() {
        List<ConfidentialRule> allRules = confidentialRuleRepository.findAllByTenantIdAndEnabled(SecurityUtils.getTenantId(), false);
        allRules.forEach(r -> r.setEnabled(true));
        confidentialRuleRepository.saveAll(allRules);
    }


    @Override
    public Set<Long> effectOnUpdate(Long id, ConfidentialRuleDto dto) {
        dto.validate();
        ConfidentialRule oldRule = confidentialRuleRepository.findByIdAndTenantId(id, SecurityUtils.getTenantId())
                .orElseThrow(() -> new NotFoundException("Rule not found"));

        List<ConfidentialTalent> confidentialTalents = confidentialTalentRepository.findAllByRuleIdAndActive(id, true);
        if (confidentialTalents.isEmpty()) {
            return Collections.emptySet();
        }

        ConfidentialRule newRule = toRuleEntity(dto);
        if (oldRule.sameRule(newRule)) {
            return Collections.emptySet();
        }

        Map<Long, ConfidentialTalent> confidentialTalentMap = confidentialTalents.stream()
                .collect(Collectors.toMap(ConfidentialTalent::getTalentId, Function.identity()));

        Set<Long> talentIds = confidentialTalentMap.keySet();
        List<TalentConfidentialCondition> confidentialConditions = getConfidentialConditions(talentIds);
        if (confidentialConditions.isEmpty()) {
            return Collections.emptySet();
        }

        Set<Long> result = new HashSet<>();
        for (TalentConfidentialCondition condition : confidentialConditions) {
            // 候选人不匹配保密规则了
            if (!condition.matchRules(newRule)) {
                result.add(condition.getId());
                if (result.equals(talentIds)) {
                    return result;
                }
            }
            // 自动解除保密周期缩短了，并且保密开始时间到当前时间的间隔小于新规则的 DeclassifyDays，会立即自动解除保密
            ConfidentialTalent confidentialTalent = confidentialTalentMap.get(condition.getId());
            Instant newDeclassifyTime = confidentialTalent.getConfidentialStartTime()
                    .plus(dto.getDeclassifyDays(), TimeUnit.DAYS.toChronoUnit());
            if (newDeclassifyTime.isBefore(Instant.now())) {
                result.add(condition.getId());
                if (result.equals(talentIds)) {
                    return result;
                }
            }
            // 新规则设置了流程自动解除, 判断候选人的已有流程是否会立即自动解除保密
            if (newRule.hasDeclassifyProcessRule()) {
                List<TalentRecruitmentProcessVO> talentRecruitmentProcessList =
                        applicationService.getTalentRecruitmentProcessAllByTalentId(condition.getId()).getBody();
                // 候选人还没有流程，不会影响自动解除
                if (talentRecruitmentProcessList == null || talentRecruitmentProcessList.isEmpty()) {
                    continue;
                }
                boolean willDeclassifyImmediately = talentRecruitmentProcessList.stream()
                        .anyMatch(process -> {
                            // 1. 是否符合新规则的流程类型
                            boolean processMatch = dto.getDeclassifyProcessIds().contains(process.getRecruitmentProcessId());
                            // 2. 是否已经达到新规则要求的最低流程节点
                            boolean nodeTypeMatch = dto.getDeclassifyProcessTypes().stream()
                                    .anyMatch(nodeType -> process.getLastNodeType().toDbValue() > nodeType.toDbValue());
                            // 3. 是否已经达到新的 DeclassifyDays 设定的时间要求
                            boolean timeMatch = dto.getDeclassifyDays() != null &&
                                    process.getLastNodeDate().isBefore(Instant.now().minus(dto.getDeclassifyDays(), ChronoUnit.DAYS));
                            return processMatch && nodeTypeMatch && timeMatch;
                        });
                if (willDeclassifyImmediately) {
                    result.add(condition.getId());
                }
            }
        }

        return result;
    }

    @Override
    public Set<Long> effectOnDelete(Long id) {
        List<ConfidentialTalent> confidentialTalents = confidentialTalentRepository.findAllByRuleIdAndActive(id, true);
        return confidentialTalents.stream().map(ConfidentialTalent::getTalentId).collect(Collectors.toSet());
    }

    @Override
    public ConfidentialInfoDto confidentialTalent(Long talentId) {
        DefaultTransactionDefinition definition = new DefaultTransactionDefinition();
        TransactionStatus status = transactionManager.getTransaction(definition);
        ConfidentialInfoDto confidentialInfoDto;
        try {
            confidentialInfoDto = processConfidentialTalent(
                    talentId,
                    // 批量查询获取 TalentConfidentialCondition
                    () -> getConfidentialConditions(Collections.singleton(talentId))
                            .stream().findAny().orElseThrow(() -> new NotFoundException("Talent not found")),
                    this::toConfidentialInfo
            );
            transactionManager.commit(status);
            sendConfidentialMessage(talentId);
        } catch (Exception e) {
            transactionManager.rollback(status);
            throw e;
        }
        return confidentialInfoDto;
    }

    @Override
    public void confidentialTalent(TalentV3 talent) {
        DefaultTransactionDefinition definition = new DefaultTransactionDefinition();
        TransactionStatus status = transactionManager.getTransaction(definition);
        try {
            processConfidentialTalent(
                    talent.getId(),
                    // 直接获取其保密条件（已经包含关联数据，无需重复查询）
                    () -> getConfidentialCondition(talent),
                    confidentialTalent -> null
            );
            transactionManager.commit(status);
            sendConfidentialMessage(talent.getId());
        } catch (Exception e) {
            transactionManager.rollback(status);
            throw e;
        }
    }

    private void sendConfidentialMessage(Long talentId) {
        SecurityContext context = SecurityContextHolder.getContext();
        CompletableFuture.runAsync(() -> {
            SecurityContextHolder.setContext(context);
            // 查询当前用户团队的合伙人
            Long profitTeamLeader = userService.findProfitTeamLeaderIdByUserId(SecurityUtils.getUserId()).getBody();
            if (profitTeamLeader == null) {
                log.error("[sendConfidentialMessage] profitTeamLeader is null");
                throw new CustomParameterizedException("profitTeamLeader is null");
            }
            TalentDTOV3 talentInfo = talentService.findTalentById(talentId, false);
            MessageCreateWithConfidentialTalent message = new MessageCreateWithConfidentialTalent(talentInfo);
            message.setConfidentialOwnerId(SecurityUtils.getUserId());
            message.setConfidentialOwnerName(SecurityUtils.getFullName());
            message.setNeedRemindUserId(profitTeamLeader);

        });
    }

    @Override
    @Transactional
    public boolean tryDeclassifyTalentByTalentUpdate(TalentV3 talent) {
        Optional<ConfidentialTalent> confidentialTalentOptional = confidentialTalentRepository.findByTalentIdAndActive(talent.getId(), true);
        // 候选人没有保密
        if (confidentialTalentOptional.isEmpty()) {
            return false;
        }
        TalentConfidentialCondition confidentialCondition = getConfidentialCondition(talent);
        ConfidentialTalent confidentialTalent = confidentialTalentOptional.get();
        ConfidentialRule confidentialRule = confidentialTalent.getRule();
        // 如果仍然满足保密规则，则不做任何操作
        if (confidentialCondition.matchRules(confidentialRule)) {
            return false;
        }
        confidentialTalent.inactive(TalentDeclassifyType.RESUME_NOMATCH);
        confidentialTalentRepository.save(confidentialTalent);
        // 删除保密定时任务
        xxlJobService.deleteTalentDeclassifyJob(talent.getId());
        // 重新同步候选人到 ES
        talentSyncService.syncTalentsToMQ(Set.of(talent.getId()), 1);
        return true;
    }

    @Override
    @Transactional
    public void declassifyTalent(Long talentId, TalentDeclassifyType declassifyReason) {
        ConfidentialTalent confidentialTalent = confidentialTalentRepository.findByTalentIdAndActive(talentId, true)
                .orElseThrow(() -> new CustomParameterizedException("Talent has not been confidential"));
        // 只有保密归属者可以手动解除保密
        if (TalentDeclassifyType.PROACTIVELY.equals(declassifyReason) && !canDeclassifyTalent(confidentialTalent)) {
            throw new ForbiddenException("no permission to declassify talent");
        }
        confidentialTalent.inactive(declassifyReason);
        confidentialTalentRepository.save(confidentialTalent);
        // 删除保密定时任务
        xxlJobService.deleteTalentDeclassifyJob(talentId);
        // 重新同步候选人到 ES
        talentSyncService.syncTalentsToMQ(Set.of(talentId), 1);
    }

    // 推断是否可以手动解除保密的方法
    private boolean canDeclassifyTalent(ConfidentialTalent confidentialTalent) {
        // 管理员可以解除保密
        if (SecurityUtils.isAdmin()) {
            return true;
        }
        // 保密拥有者可以解除
        if (confidentialTalent.getConfidentialOwner().equals(SecurityUtils.getUserId())) {
            return true;
        }
        TeamDataPermissionRespDTO dataPermission = initiationService.initiateConfidentialTalentDataPermissionByUserId(SecurityUtils.getTenantId(), SecurityUtils.getUserId()).getBody();
        // 保密数据权限是 all , 可以解除
        if (dataPermission != null) {
            if (Boolean.TRUE.equals(dataPermission.getAll())) {
                return true;
            }
            // 保密数据权限是 team，如果保密拥有者在自己团队就可以解除
            if (Boolean.FALSE.equals(dataPermission.getSelf())) {
                List<UserBriefDTO> permissionUsers = userService.getAllBriefUsersWithPermissionByType(Module.CONFIDENTIAL_TALENT).getBody();
                return permissionUsers.stream().map(UserBriefDTO::getId).anyMatch(id -> id.equals(confidentialTalent.getConfidentialOwner()));
            }
        }

        return false;
    }


    @Override
    public boolean confidentialTalentViewAble(Long talentId) {
        return filterViewableTalentIds(Set.of(talentId)).contains(talentId);
    }

    @Override
    public Set<Long> filterViewableTalentIds(Set<Long> talentIds) {
        // 管理员
        if (SecurityUtils.isAdmin()) {
            return talentIds;
        }
        TeamDataPermissionRespDTO dataPermission = initiationService.initiateConfidentialTalentDataPermissionByUserId(SecurityUtils.getTenantId(), SecurityUtils.getUserId()).getBody();
        if (dataPermission == null) {
            throw new CustomParameterizedException("Cannot find data permission");
        }
        // 保密数据权限是 all, 可以查看所有保密候选人
        if (Boolean.TRUE.equals(dataPermission.getAll())) {
            return talentIds;
        }

        Set<Long> viewableTalentIds = new HashSet<>();

        //候选人没有保密
        List<ConfidentialTalent> confidentialTalents = confidentialTalentRepository.findAllByTalentIdInAndActive(talentIds, true);
        Set<Long> confidentialTalentIds =  confidentialTalents.stream().map(ConfidentialTalent::getTalentId).collect(Collectors.toSet());
        talentIds.stream().filter(tid -> !confidentialTalentIds.contains(tid)).forEach(viewableTalentIds::add);
        if (talentIds.equals(viewableTalentIds)) {
            return viewableTalentIds;
        }

        // team 数据权限
        if (Boolean.FALSE.equals(dataPermission.getAll())) {
            List<UserBriefDTO> usersWithPermission = userService.getAllBriefUsersWithPermissionByType(Module.CONFIDENTIAL_TALENT).getBody();
            if (CollectionUtils.isNotEmpty(usersWithPermission)) {
                Set<Long> permissionUserIds = usersWithPermission.stream().map(UserBriefDTO::getId).collect(Collectors.toSet());
                confidentialTalents.stream().filter(t -> permissionUserIds.contains(t.getConfidentialOwner()))
                        .map(ConfidentialTalent::getTalentId).forEach(viewableTalentIds::add);
                if (talentIds.equals(viewableTalentIds)) {
                    return viewableTalentIds;
                }
            }
        }

        // 候选人保密拥有者
        confidentialTalents.stream().filter(t -> t.getConfidentialOwner().equals(SecurityUtils.getUserId()))
                .map(ConfidentialTalent::getTalentId).forEach(viewableTalentIds::add);
        if (talentIds.equals(viewableTalentIds)) {
            return viewableTalentIds;
        }

        // 候选人创建者
        Set<Long> creatorTalents = new HashSet<>(talentRepository.filterByPermissionUserId(talentIds, SecurityUtils.getUserId()));
        viewableTalentIds.addAll(creatorTalents);
        if (talentIds.equals(viewableTalentIds)) {
            return viewableTalentIds;
        }

        // 候选人拥有者、分享者、流程拥有者
        Map<Long, List<TalentOwnership>> talentOwnershipMap = talentOwnershipRepository.findAllByTalentIdIn(new ArrayList<>(talentIds))
                .stream().collect(Collectors.groupingBy(TalentOwnership::getTalentId, Collectors.toList()));
        talentIds.stream()
                .filter(tid -> talentOwnershipMap.getOrDefault(tid, Collections.emptyList())
                        .stream().anyMatch(o -> o.getUserId().equals(SecurityUtils.getUserId())
                                || o.getOwnershipType().equals(TalentOwnershipType.TENANT_SHARE)))
                .forEach(viewableTalentIds::add);
        if (talentIds.equals(viewableTalentIds)) {
            return viewableTalentIds;
        }

        // 候选人流程参与者
        Set<Long> validTalentsByParticipant = applicationService.filterTalentIdsByUserIdAndTalentIds(SecurityUtils.getUserId(), talentIds).getBody();
        if (validTalentsByParticipant != null) {
            viewableTalentIds.addAll(validTalentsByParticipant);
        }

        return viewableTalentIds;
    }

    @Override
    public Optional<ConfidentialInfoDto> getConfidentialInfo(Long talentId) {
        return confidentialTalentRepository.findByTalentIdAndActive(talentId, true).map(this::toConfidentialInfo);
    }

    @Override
    public Map<Long, ConfidentialInfoDto> getConfidentialInfoBatch(Set<Long> talentIds) {
        return confidentialTalentRepository.findAllByTalentIdInAndActive(talentIds, true)
                .stream().collect(Collectors.toMap(ConfidentialTalent::getTalentId, this::toConfidentialInfo));
    }

    @Override
    @Transactional
    public void tryAutoDeclassifyTalentByProcess(TalentAutoDeclassifyDto talentAutoDeclassifyDto) {
        Long talentId = talentAutoDeclassifyDto.getTalentId();
        Optional<ConfidentialTalent> confidentialTalentOpt = confidentialTalentRepository.findByTalentIdAndActive(talentId, true);
        if (confidentialTalentOpt.isEmpty()) {
            log.info("Talent {} not confidential", talentId);
            return;
        }
        ConfidentialTalent confidentialTalent = confidentialTalentOpt.get();
        ConfidentialRule confidentialRule = confidentialTalent.getRule();
        DeclassificationRules declassificationRules = confidentialRule.getDeclassificationRules();
        if (declassificationRules == null) {
            log.info("declassificationRules is null, talentId: {}", talentId);
            return;
        }

        Set<Long> declassifyProcessIds = declassificationRules.getDeclassifyProcessIds();
        if (declassifyProcessIds == null || !declassifyProcessIds.contains(talentAutoDeclassifyDto.getRecruitmentProcessId())) {
            log.info("Talent {} not match declassifyProcessIds: {}", talentId, declassifyProcessIds);
            return;
        }

        Set<NodeType> declassifyProcessTypes = declassificationRules.getDeclassifyProcessTypes();
        if (declassifyProcessTypes == null || !declassifyProcessTypes.contains(talentAutoDeclassifyDto.getCurrentNode())) {
            log.info("Talent {} not match declassifyProcessTypes: {}", talentId, declassifyProcessTypes);
            return;
        }

        Integer declassifyProcessDelayDays = declassificationRules.getDeclassifyProcessDelayDays();
        if (declassifyProcessDelayDays == null) {
            throw new CustomParameterizedException("declassifyProcessDelayDays is null");
        }

        if (declassifyProcessDelayDays > 0) {
            confidentialTalent.setConfidentialEndTime(Instant.now().plus(declassifyProcessDelayDays, ChronoUnit.DAYS));
            confidentialTalentRepository.save(confidentialTalent);
            xxlJobService.addTalentDeclassifyJob(confidentialTalent, TalentDeclassifyType.DECLASSIFY_PROCESS,
                    String.valueOf(talentAutoDeclassifyDto.getTalentRecruitmentProcessId()));
        } else {
            confidentialTalent.inactive(TalentDeclassifyType.DECLASSIFY_PROCESS,
                    String.valueOf(talentAutoDeclassifyDto.getTalentRecruitmentProcessId()));
            confidentialTalentRepository.saveAndFlush(confidentialTalent);
            xxlJobService.deleteTalentDeclassifyJob(talentId);
            // 重新同步候选人到 ES
            talentSyncService.syncTalentsToMQ(Set.of(talentId), 1);
        }
    }

    @Override
    @Transactional
    public void autoDeclassifyTalent(TalentAutoDeclassifyDto talentAutoDeclassifyDto) {
        Long talentId = talentAutoDeclassifyDto.getTalentId();
        ConfidentialTalent confidentialTalent = confidentialTalentRepository.findByTalentIdAndActive(talentId, true)
                .orElseThrow(() -> new NotFoundException("Talent not confidential"));
        switch (talentAutoDeclassifyDto.getDeclassifyType()) {
            case MATURITY -> confidentialTalent.inactive(TalentDeclassifyType.MATURITY);
            case DECLASSIFY_PROCESS -> confidentialTalent.inactive(TalentDeclassifyType.DECLASSIFY_PROCESS,
                    String.valueOf(talentAutoDeclassifyDto.getTalentRecruitmentProcessId()));
            default -> throw new CustomParameterizedException("Declassify type not allowed");
        }
        confidentialTalentRepository.saveAndFlush(confidentialTalent);
        xxlJobService.deleteTalentDeclassifyJob(talentId);
        // 重新同步候选人到 ES
        talentSyncService.syncTalentsToMQ(Set.of(talentId), 1);
    }

    @Override
    public void handoverConfidentialTalent(Long fromUser, Long toUser) {
        List<ConfidentialTalent> confidentialTalents = confidentialTalentRepository.findAllByConfidentialOwnerAndActive(fromUser, true);
        if (confidentialTalents.isEmpty()) {
            return;
        }
        confidentialTalents.forEach(t -> t.handover(toUser));
        Set<Long> talentIds = confidentialTalents.stream().map(ConfidentialTalent::getTalentId).collect(Collectors.toSet());
        confidentialTalentRepository.saveAllAndFlush(confidentialTalents);
        talentSyncService.syncTalentsToMQ(talentIds, 1);
    }

    private void checkSameRule(@NotNull ConfidentialRule rule) {
        List<ConfidentialRule> allRule = confidentialRuleRepository.findAllByTenantId(SecurityUtils.getTenantId());
        if (allRule.stream().filter(r -> !r.getId().equals(rule.getId()))
                .noneMatch(r -> r.sameRule(rule))) {
            return;
        }
        String i18nMsg = commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(
                TalentAPIMultilingualEnum.CONFIDENTIAL_RULE_DUPLICATE.getKey(),
                CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),
                talentApiPromptProperties.getTalentService());
        throw new CustomParameterizedException(i18nMsg);
    }


    /**
     * 通用处理方法：
     * - talentId：候选人的ID
     * - conditionSupplier：提供 TalentConfidentialCondition
     * - resultMapper：处理后的 ConfidentialTalent 转换为调用者需要的返回类型
     * 该方法封装了权限校验、规则匹配、保密实体更新以及定时任务添加的逻辑。
     */
    private <R> R processConfidentialTalent(Long talentId,
                                            Supplier<TalentConfidentialCondition> conditionSupplier,
                                            Function<ConfidentialTalent, R> resultMapper) {
        // 获取保密条件
        TalentConfidentialCondition condition = conditionSupplier.get();

        // 权限校验：只有候选人创建者、拥有者以及管理员才能进行保密操作
        if (!SecurityUtils.isAdmin()) {
            List<TalentOwnership> ownerships = talentOwnershipRepository.findAllByTalentId(talentId);
            List<TalentOwnershipType> talentOwner = List.of(TalentOwnershipType.OWNER, TalentOwnershipType.TALENT_OWNER);
            boolean nonOwner = ownerships.stream()
                    .noneMatch(o -> o.getUserId().equals(SecurityUtils.getUserId()) && talentOwner.contains(o.getOwnershipType()));
            boolean nonCreator = talentRepository.filterByPermissionUserId(Collections.singleton(talentId), SecurityUtils.getUserId())
                    .isEmpty();
            if (nonCreator && nonOwner) {
                throw new ForbiddenException("no permission to confidential talent");
            }
        }

        Optional<ConfidentialTalent> confidentialTalentOptional = confidentialTalentRepository.findByTalentId(talentId);
        if (confidentialTalentOptional.isPresent()) {
            ConfidentialTalent existingConfidentialTalent = confidentialTalentOptional.get();
            // 候选人已经保密了，则不允许再次保密
            if (existingConfidentialTalent.isActive()) {
                throw new CustomParameterizedException("Talent has been confidential");
            }
        }

        // 根据保密条件匹配自动解除保密时间最长的规则
        List<ConfidentialRule> rules = confidentialRuleRepository.findAllEnableRules(SecurityUtils.getTenantId());
        ConfidentialRule rule = rules.stream()
                .filter(condition::matchRules)
                .max(Comparator.comparing((ConfidentialRule r) -> r.getDeclassificationRules().getDeclassifyDays())
                        .thenComparing(AbstractAuditingEntity::getLastModifiedDate)
                        .thenComparing(ConfidentialRule::getId))
                .orElseThrow(() -> {
                    String i18nMsg = commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(
                            TalentAPIMultilingualEnum.TALENT_NOT_MATCH_CONFIDENTIAL_RULE.getKey(),
                            CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),
                            talentApiPromptProperties.getTalentService());
                    return new CustomParameterizedException(i18nMsg);
                });

        ConfidentialTalent confidentialTalent = confidentialTalentOptional.orElse(new ConfidentialTalent());
        confidentialTalent.activeConfidential(talentId, rule);

        // 保存更新，并增加定时任务以自动解除保密
        confidentialTalentRepository.save(confidentialTalent);

        xxlJobService.addTalentDeclassifyJob(confidentialTalent, TalentDeclassifyType.MATURITY);

        // 获取用户所在团队以及上级团队的所有 team leader，自动把他们设置为候选人的分享者
        autoShareTalent(talentId);
        return resultMapper.apply(confidentialTalent);
    }

    private void autoShareTalent(Long talentId) {
        Set<Long> teamLeaders = userService.getUpperTeamLeadersByUserId(SecurityUtils.getUserId()).getBody();
        if (teamLeaders == null || teamLeaders.isEmpty()) {
            return;
        }
        List<TalentOwnership> ownerships = talentOwnershipRepository.findAllByTalentId(talentId);
        boolean shareToAll = ownerships.stream().anyMatch(o -> TalentOwnershipType.TENANT_SHARE.equals(o.getOwnershipType()));
        if (shareToAll) {
            return;
        }
        Set<Long> existUserIds = ownerships.stream().map(TalentOwnership::getUserId).collect(Collectors.toSet());
        Set<Long> needAddUserIds = teamLeaders.stream().filter(userId -> !existUserIds.contains(userId)).collect(Collectors.toSet());
        if (needAddUserIds.isEmpty()) {
            return;
        }
        Instant expireTime = ownerships.stream().filter(o -> TalentOwnershipType.OWNER.equals(o.getOwnershipType()))
                .map(TalentOwnership::getExpireTime).findFirst()
                .orElse(Instant.now().plus(3, ChronoUnit.DAYS));
        List<TalentOwnership> newOwnerships = needAddUserIds.stream()
                .map(userId -> {
                    TalentOwnership talentOwnership = new TalentOwnership();
                    talentOwnership.setTalentId(talentId);
                    talentOwnership.setUserId(userId);
                    talentOwnership.setOwnershipType(TalentOwnershipType.SHARE);
                    talentOwnership.setExpireTime(expireTime);
                    return talentOwnership;
                })
               .toList();
        talentOwnershipRepository.saveAllAndFlush(newOwnerships);

        talentCrmSyncService.syncClientOwnershipToCrm(talentId);
    }

    private TalentConfidentialCondition getConfidentialCondition(TalentV3 talent) {
        Pair<List<EnumCurrency>, List<EnumDegree>> enumPair = getAllEnumCurrencyAndDegree();
        return toConditionEntity(talent, enumPair.getFirst(), enumPair.getSecond());
    }


    private List<TalentConfidentialCondition> getConfidentialConditions(Set<Long> talentIds) {
        if (CollectionUtils.isEmpty(talentIds)) {
            return Collections.emptyList();
        }
        List<TalentV3> talents = talentRepository.findAllByIdIsIn(talentIds);
        Pair<List<EnumCurrency>, List<EnumDegree>> enumPair = getAllEnumCurrencyAndDegree();
        return talents.stream().map(talent -> toConditionEntity(talent, enumPair.getFirst(), enumPair.getSecond())).toList();
    }

    private Pair<List<EnumCurrency>, List<EnumDegree>> getAllEnumCurrencyAndDegree() {
        List<EnumCurrency> allEnumCurrency = enumCommonService.findAllEnumCurrency();
        List<EnumDegree> allDegrees = enumCommonService.findAllEnumDegree();
        return Pair.of(allEnumCurrency, allDegrees);
    }

    private TalentConfidentialCondition toConditionEntity(TalentV3 talent, List<EnumCurrency> allEnumCurrency, List<EnumDegree> allDegrees) {
        TalentConfidentialCondition condition = new TalentConfidentialCondition(allDegrees, allEnumCurrency);
        condition.setId(talent.getId());
        condition.setIndustries(talent.getIndustries());
        condition.setJobFunctions(talent.getJobFunctions());
        String extendedInfo = talent.getTalentAdditionalInfo().getExtendedInfo();
        JSONObject jsonObject = JSONUtil.parseObj(extendedInfo);
        JSONArray educations = jsonObject.getJSONArray("educations");
        if (educations != null) {
            condition.setEducations(educations.toList(TalentEducationDTO.class));
        }
        String payType = jsonObject.getStr("payType");
        if (payType != null) {
            condition.setPayType(RateUnitType.fromEnumValue(payType));
        }
        condition.setCurrency(jsonObject.getStr("currency"));
        condition.setSalaryRange(jsonObject.getJSONObject("salaryRange"));
        return condition;

    }


    private ConfidentialRule toRuleEntity(ConfidentialRuleDto dto) {
        ConfidentialRule rule = new ConfidentialRule();
        rule.setId(dto.getId());
        rule.fillWithDto(dto);
        return rule;
    }

    private ConfidentialRuleDto toRuleDto(ConfidentialRule rule) {
        ConfidentialRuleDto dto = new ConfidentialRuleDto();
        dto.fillWithEntity(rule);
        return dto;
    }

    private ConfidentialInfoDto toConfidentialInfo(ConfidentialTalent confidentialTalent) {
        ConfidentialInfoDto confidentialInfoDto = new ConfidentialInfoDto();
        confidentialInfoDto.setStartTime(confidentialTalent.getConfidentialStartTime());
        confidentialInfoDto.setEndTime(confidentialTalent.getConfidentialEndTime());
        confidentialInfoDto.setConfidentialOwner(confidentialTalent.getConfidentialOwner());
        return confidentialInfoDto;
    }
}
