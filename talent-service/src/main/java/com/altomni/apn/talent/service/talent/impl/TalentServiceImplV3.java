package com.altomni.apn.talent.service.talent.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.altomni.apn.common.config.CommonApiMultilingualConfig;
import com.altomni.apn.common.config.application.ApplicationIPGProperties;
import com.altomni.apn.common.config.constants.ElasticSearchConstants;
import com.altomni.apn.common.constants.ResponsibilityConstants;
import com.altomni.apn.common.domain.dict.EnumDegree;
import com.altomni.apn.common.domain.dict.EnumLanguage;
import com.altomni.apn.common.domain.dict.EnumUserResponsibility;
import com.altomni.apn.common.domain.enumeration.ContactType;
import com.altomni.apn.common.domain.enumeration.LanguageEnum;
import com.altomni.apn.common.domain.enumeration.RecommendFeedbackReason;
import com.altomni.apn.common.domain.enumeration.application.NodeStatus;
import com.altomni.apn.common.domain.enumeration.application.NodeType;
import com.altomni.apn.common.domain.enumeration.search.ModuleType;
import com.altomni.apn.common.domain.enumeration.user.Status;
import com.altomni.apn.common.domain.talent.TalentAssociationJobFolderTalent;
import com.altomni.apn.common.domain.talent.TalentContact;
import com.altomni.apn.common.domain.user.Tenant;
import com.altomni.apn.common.dto.RecommendFeedback;
import com.altomni.apn.common.dto.application.talentrecruitmentprocess.TalentRecruitmentProcessVO;
import com.altomni.apn.common.dto.http.HttpResponse;
import com.altomni.apn.common.dto.permission.TeamDataPermissionRespDTO;
import com.altomni.apn.common.dto.search.*;
import com.altomni.apn.common.dto.talent.GetTalentsForAiRecommendationDTO;
import com.altomni.apn.common.dto.talent.TalentContactDTO;
import com.altomni.apn.common.dto.user.UserBriefDTO;
import com.altomni.apn.common.enumeration.enums.TalentAPIMultilingualEnum;
import com.altomni.apn.common.enumeration.enums.TalentPreferenceColumnConfig;
import com.altomni.apn.common.enumeration.permission.Module;
import com.altomni.apn.common.errors.CustomParameterizedException;
import com.altomni.apn.common.errors.NotFoundException;
import com.altomni.apn.common.repository.talent.TalentRelateJobFolderRepository;
import com.altomni.apn.common.repository.talent.TalentRelateJobFolderTalentRepository;
import com.altomni.apn.common.service.cache.CachePermission;
import com.altomni.apn.common.service.cache.CommonRedisService;
import com.altomni.apn.common.service.cache.EnumCommonService;
import com.altomni.apn.common.service.enums.EnumIndustryService;
import com.altomni.apn.common.service.enums.EnumJobFunctionService;
import com.altomni.apn.common.service.enums.EnumUserResponsibilityService;
import com.altomni.apn.common.service.initiation.InitiationService;
import com.altomni.apn.common.utils.CommonUtils;
import com.altomni.apn.common.utils.ElasticSearchUtil;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.talent.config.env.ApplicationProperties;
import com.altomni.apn.talent.config.env.TalentApiPromptProperties;
import com.altomni.apn.talent.constants.SearchTalentEsSource;
import com.altomni.apn.talent.domain.enumeration.folder.TalentSearchCategory;
import com.altomni.apn.talent.domain.folder.TalentFolder;
import com.altomni.apn.talent.domain.folder.TalentFolderSharingTeam;
import com.altomni.apn.talent.domain.folder.TalentFolderSharingUser;
import com.altomni.apn.talent.domain.statistics.JobTalentRecommendFeedback;
import com.altomni.apn.talent.repository.folder.TalentFolderRepository;
import com.altomni.apn.talent.repository.folder.TalentFolderSharingTeamRepository;
import com.altomni.apn.talent.repository.folder.TalentFolderSharingUserRepository;
import com.altomni.apn.talent.repository.statistics.JobTalentRecommendFeedbackRepository;
import com.altomni.apn.talent.service.UserService;
import com.altomni.apn.talent.service.application.ApplicationService;
import com.altomni.apn.talent.service.confidential.TalentConfidentialService;
import com.altomni.apn.talent.service.dto.folder.TalentFolderSharingTeamDTO;
import com.altomni.apn.talent.service.dto.talent.CategoryCountDTO;
import com.altomni.apn.talent.service.dto.talent.TalentCategoryCountRequestDTO;
import com.altomni.apn.talent.service.elastic.EsCommonService;
import com.altomni.apn.talent.service.elastic.EsFillerTalentService;
import com.altomni.apn.talent.service.elastic.dto.SearchEsBySource;
import com.altomni.apn.talent.service.elastic.dto.SearchEsId;
import com.altomni.apn.talent.service.finance.FinanceService;
import com.altomni.apn.talent.service.management.ManagementService;
import com.altomni.apn.talent.service.mapper.folder.TalentFolderSharingTeamMapper;
import com.altomni.apn.talent.service.talent.TalentService;
import com.altomni.apn.talent.service.talent.TalentServiceV3;
import com.altomni.apn.talent.web.rest.talent.dto.SearchTalentSourceInput;
import com.altomni.apn.user.domain.user.CreditTransaction;
import com.altomni.apn.user.service.dto.customconfig.CustomColumnField;
import com.altomni.apn.user.service.dto.customconfig.TalentColumnConfigDTO;
import com.altomni.apn.user.service.dto.customconfig.UserCustomConfig;
import com.fasterxml.jackson.core.JsonProcessingException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.SetUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.io.IOException;
import java.time.LocalDate;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.altomni.apn.common.config.constants.Constants.GENERALTEXT;
import static com.altomni.apn.common.config.constants.RedisConstants.DATA_KEY_TALENT_SEARCH_HISTORY;

@Slf4j
@Service("talentServiceV3")
public class TalentServiceImplV3 implements TalentServiceV3 {

    @Resource
    private UserService userService;

    @Resource
    private EsCommonService esCommonService;

    @Resource
    private EnumIndustryService enumIndustryService;

    @Resource
    private EnumJobFunctionService enumJobFunctionService;

    @Resource
    private EnumUserResponsibilityService enumUserResponsibilityService;

    @Resource
    private CommonRedisService commonRedisService;

    @Resource
    private EsFillerTalentService esFillerTalentService;

    @Resource
    private ApplicationProperties applicationProperties;

    @Resource
    private EnumCommonService enumCommonService;

    @Resource
    private TalentFolderRepository talentFolderRepository;

    @Resource
    private TalentFolderSharingUserRepository talentFolderSharingUserRepository;

    @Resource
    private ManagementService managementService;

    @Resource
    private TalentFolderSharingTeamRepository talentFolderSharingTeamRepository;

    @Resource
    CommonApiMultilingualConfig commonApiMultilingualConfig;

    @Resource
    TalentApiPromptProperties talentApiPromptProperties;

    @Resource
    TalentFolderSharingTeamMapper talentFolderSharingTeamMapper;

    //permission related service
    @Resource
    private InitiationService initiationService;
    @Resource
    private CachePermission cachePermission;

    @Resource
    private FinanceService financeService;

    @Resource
    private TalentConfidentialService talentConfidentialService;

    @Override
    public String searchTalentFromES(TalentSearchConditionDTO condition, Pageable pageable, HttpHeaders headers, boolean checkPermission) throws IOException {
        Tenant tenant = managementService.queryTenant(SecurityUtils.getTenantId()).getBody();
        if (tenant == null || tenant.getId() == null) {
            throw new NotFoundException("This tenant does not exist");
        }
        if(checkPermission) {
            checkFoldersPermission(condition);
        }
        //fix pageInfo
        pageable = PageRequest.of(pageable.getPageNumber() - 1, pageable.getPageSize(), pageable.getSort());
        TalentSearchGroup searchGroup = new TalentSearchGroup();
        OwnershipRestriction ownershipRestriction = new OwnershipRestriction();
        ownershipRestriction.setTenantId(SecurityUtils.getTenantId());
        ownershipRestriction.setUserId(SecurityUtils.getUserId());
        ownershipRestriction.setSearchUnlimitedOwnedData(true);
        searchGroup.setOwnershipRestriction(ownershipRestriction);
//        }
        searchGroup.setModule(condition.getModule().getName());
        searchGroup.setLanguage(condition.getLanguage().toDbValue());
        searchGroup.setTimeZone(condition.getTimezone());
        SearchFilterDTO filterDTO = new SearchFilterDTO();
        if (ModuleType.RELATE_JOB_FOLDER.equals(condition.getModule())) {
            if (condition.getFolderIds() == null) {
                throw new CustomParameterizedException("RELATE_JOB_FOLDER need input folderIds");
            }
            JobFolderSearchFilter jobFolderSearchFilter = getRelateJobFolderTalent(condition.getSearch(), condition.getFolderIds(), condition.getBelongToRelateJobFolder());
            filterDTO.setJobFolderSearchFilter(jobFolderSearchFilter);
        }
        filterDTO.setQueryFilter(condition.getFilter());
        TalentColumnConfigDTO columnConfig = getColumnConfig(condition.getModule());
        List<String> source = filterSearchSource(columnConfig, condition.getModule());
        if (ModuleType.COMMON_POOL.equals(condition.getModule())) {
            //已经没有转换，前端直接传common pool jobFunctions/industry names
//            //TODO 2.5版本临时转换
//            formatSearchGroup(condition.getSearch());

            //---
            if (condition.getCommonPoolType() != null) {
                PurchaseFilterDTO purchaseFilterDTO = new PurchaseFilterDTO();
                purchaseFilterDTO.setType(condition.getCommonPoolType());
                purchaseFilterDTO.setTenantIds(Collections.singletonList(SecurityUtils.getTenantId()));
                filterDTO.setPurchaseFilter(purchaseFilterDTO);
            }
        } else {
            List<String> sourceList = new ArrayList<>();
            if (Objects.equals(condition.getLanguage(), LanguageEnum.EN)) {
                ElasticSearchConstants.EN_OR_CH_SORT_KEY_FOR_TALENT.forEach(sortKye -> sourceList.add(sortKye + ElasticSearchConstants.EN_SORT_KEY));
            } else {
                ElasticSearchConstants.EN_OR_CH_SORT_KEY_FOR_TALENT.forEach(sortKye -> sourceList.add(sortKye + ElasticSearchConstants.CN_SORT_KEY));
            }
            source.addAll(sourceList);
            searchGroup.setIndex(com.altomni.apn.common.config.constants.Constants.INDEX_TALENTS + SecurityUtils.getTenantId());
        }
        searchGroup.setSearch(condition.getSearch());
        List<String> folderIds = condition.getFolderIds();
        //ModuleType为RELATE_JOB_FOLDER,需要添加folderId到jobFolderSearchFilter中
        if (folderIds != null && !ModuleType.RELATE_JOB_FOLDER.equals(condition.getModule())) {
            filterDTO.setFolders(folderIds);
        }
        filterDTO.setSource(source);
        searchGroup.setFilter(filterDTO);
        if (!ModuleType.COMMON_POOL.equals(condition.getModule())) {
            addConfidentialFilter(searchGroup);
        }
        //format search param
        List<EnumUserResponsibility> userResponsibilityList = enumUserResponsibilityService.findAllUserResponsibility();

        //1.我的/所有候选人中的候选人列表  2. 搜索文件夹中的候选人列表  5.关联至职位中   不隐藏
        //3. job模块选到job中的添加候选人里的候选人列表 4.关联至职位中添加候选人家条件隐藏
        HttpResponse response = null;
        if (ModuleType.JOB_PICK_CANDIDATE.equals(condition.getModule()) || (ModuleType.RELATE_JOB_FOLDER.equals(condition.getModule()) && Boolean.FALSE.equals(condition.getBelongToRelateJobFolder()))) {
            searchGroup = addContactSearchDataPermission(searchGroup);
        }
        response = esCommonService.searchTalentFromCommonService(searchGroup, pageable, userResponsibilityList);

        if (ObjectUtil.isEmpty(response) || ObjectUtil.isEmpty(response.getBody())) {
            headers.set("Pagination-Count", String.valueOf(0));
            return "[]";
        } else {
            headers.set("Pagination-Count", ElasticSearchUtil.getObjectCountFromResponseHeader(response.getHeaders()) + "");
            headers.set("Pagination-Owned-Data-Count", ElasticSearchUtil.getTotalOwnedDataCountFromResponseHeader(response.getHeaders()) + "");
            headers.set("Pagination-Not-Owned-Data-Count", ElasticSearchUtil.getTotalNotOwnedDataCountFromResponseHeader(response.getHeaders()) + "");
            if (ModuleType.COMMON_POOL.equals(condition.getModule())) {
                return formatCommonPoolData(response.getBody());
            } else if (ModuleType.RELATE_JOB_FOLDER.equals(condition.getModule())) {
                return formatRelateJobFolderTalentData(response.getBody(), pageable, folderIds.get(0));
            } else {
                return formatTalentData(response.getBody(), pageable);
            }
        }
    }

    private void addConfidentialFilter(TalentSearchGroup searchGroup) {
        if (SecurityUtils.isAdmin()) {
            return;
        }
        TeamDataPermissionRespDTO dataPermission = initiationService.initiateConfidentialTalentDataPermissionByUserId(SecurityUtils.getTenantId(), SecurityUtils.getUserId()).getBody();
        if (dataPermission == null) {
            throw new CustomParameterizedException("Cannot find data permission");
        }
        // 保密数据权限是 all, 可以查看所有保密候选人
        if (Boolean.TRUE.equals(dataPermission.getAll())) {
            return;
        }
        // 保密数据权限是 team
        Set<Long> userIds = new HashSet<>();
        if (Boolean.TRUE.equals(dataPermission.getSelf())) {
            userIds.add(SecurityUtils.getUserId());
        } else {
            List<UserBriefDTO> permissionUsers = userService.getAllBriefUsersWithPermissionByType(Module.CONFIDENTIAL_TALENT).getBody();
            if (permissionUsers == null || permissionUsers.isEmpty()) {
                log.info("permission users is null or empty, only search confidential talent fot current user");
                userIds.add(SecurityUtils.getUserId());
            } else {
                Set<Long> permissionUserIds = permissionUsers.stream().map(UserBriefDTO::getId).collect(Collectors.toSet());
                userIds.addAll(permissionUserIds);
            }
        }
        // 拥有保密候选人查看权限的过滤条件
        Stream<ConditionParam> talentOwnerShipFilter = enumCommonService.findTalentEnumUserResponsibility().stream()
                .map(responsibility -> ConditionParam.of(responsibility.getTalentEsKey() + ".id", userIds));
        Stream<ConditionParam> talentApplicationFilter = enumCommonService.findApplicationsEnumUserResponsibility().stream()
                .map(responsibility -> ConditionParam.of(responsibility.getApplicationEsKey() + ".id", userIds));

        // 不是保密候选人
        ConditionParam notConfidential = ConditionParam.of("isConfidential", false);

        // 后端整体条件为：(不是保密候选人) OR (talentOwnerShipFilter 或 talentApplicationFilter))
        List<ConditionParam> permissionConditions = Stream.concat(Stream.of(notConfidential), Stream.concat(talentOwnerShipFilter, talentApplicationFilter))
                .toList();

        SearchFilterDTO filter = searchGroup.getFilter();
        List<SearchParam> queryFilter = filter.getQueryFilter();
        if (queryFilter == null) {
            queryFilter = new ArrayList<>();
            filter.setQueryFilter(queryFilter);
        }
        queryFilter.add(SearchParam.of(Relation.OR, permissionConditions));
        searchGroup.setFilter(filter);
    }


    @Override
    public String searchTalentFromES(TalentSearchConditionDTO condition, Pageable pageable, HttpHeaders headers) throws IOException {
        return searchTalentFromES(condition, pageable, headers, true);
    }


    @Override
    public JSONArray findTalentsForAiRecommendationByIds(GetTalentsForAiRecommendationDTO dto) {
        Map<String, Double> talentIdMap = dto.getTalentIdMap();
        Set<String> talentIdSet = talentIdMap.keySet();
        List<String> talentId = new ArrayList<>();
        List<String> commonPoolEsId = new ArrayList<>();
        Map<String, CreditTransaction> map = getCreditTransactionMap(talentIdSet.stream().filter(s -> !NumberUtil.isNumber(s)).toList());
        //commonpool已解决的记录转换关系，便于拿score
        Map<String, String> talentId2EsId = new HashMap<>();
        splitCommonAndTenantId(talentIdSet, talentId, commonPoolEsId, map, talentId2EsId);
        JSONArray result = new JSONArray();
        result.addAll(getTalentSearchResult(talentId, dto.getJobId()));
        try {
            result.addAll(getCommonPoolSearchResult(commonPoolEsId, map));
        } catch (JsonProcessingException e) {
            throw new RuntimeException(e);
        }
        shortenReturn(result, talentIdMap, talentId2EsId);
        result.sort((a, b) -> {
            JSONObject objA = (JSONObject) a;
            JSONObject jsonObjectA = objA.getJSONObject("_source");
            if(jsonObjectA == null) {
                jsonObjectA = new JSONObject();
            }
            JSONObject objB = (JSONObject) b;
            JSONObject jsonObjectB = objB.getJSONObject("_source");
            if(jsonObjectB == null) {
                jsonObjectB = new JSONObject();
            }

            // 获取score值，如果不存在则默认为0.0
            double scoreA = jsonObjectA.getDouble("score", 0.0);
            double scoreB = jsonObjectB.getDouble("score", 0.0);

            // 从高到低排序，所以用scoreB减scoreA
            return Double.compare(scoreB, scoreA);
        });
        return result;
    }

    private void shortenReturn(JSONArray result, Map<String, Double>  talentIdMap, Map<String, String> talentId2EsId) {
        for(int i = 0; i < result.size(); i++) {
            JSONObject jsonObject = result.getJSONObject(i);
            String id = jsonObject.getStr("_id");
            JSONObject source = jsonObject.getJSONObject("_source");
            source.put("score", getScore(id, talentIdMap, talentId2EsId));
            JSONArray languages = source.getJSONArray("languages");
            source.put("languagesTotal", languages != null ? languages.size() : 0);
            Object oTopEducation = source.remove("topEducation");
            Object oNonTopEducation = source.remove("nonTopEducations");
            Object oCurrentExperiences = source.remove("currentExperiences");
            Object oPastExperiences = source.remove("pastExperiences");
            JSONArray educations = new JSONArray();
            JSONArray experiences = new JSONArray();
            getEducationAndExperience(educations, experiences, oTopEducation, oNonTopEducation, oCurrentExperiences, oPastExperiences);

            source.put("educations", educations);
            source.put("experiences", experiences);
        }
    }

    private Object getScore(String id, Map<String, Double> talentIdMap, Map<String, String> talentId2EsId) {
        String tId = talentId2EsId.get(id) != null ? talentId2EsId.get(id) : id;

        return talentIdMap.get(tId);
    }

    private void getEducationAndExperience(JSONArray educations, JSONArray experiences, Object oTopEducation, Object oNonTopEducation, Object oCurrentExperiences, Object oPastExperiences) {
        //教育及工作经历4条挑选逻辑请遵循以下顺序并循环，跳过没有的，直到拿满4条或者没有可拿的。
        // 1. 取topEducation 2. 取至多3条currentExperienences 3. 取1条nonTopEducation 4. 取至多2条pastExperienences
        JSONObject topEducation = oTopEducation != null ? JSONUtil.parseObj(oTopEducation) : null;
        JSONArray nonTopEducation = oNonTopEducation != null ? JSONUtil.parseArray(oNonTopEducation) : new JSONArray();
        JSONArray currentExperiences = oCurrentExperiences != null ? JSONUtil.parseArray(oCurrentExperiences) : new JSONArray();
        JSONArray pastExperiences = oPastExperiences != null ? JSONUtil.parseArray(oPastExperiences) : new JSONArray();
        Map<String, EnumDegree> degreeMap = enumCommonService.findAllEnumDegree().stream().collect(Collectors.toMap(EnumDegree::getName, e -> e));
        if(topEducation != null) {
            addDegreeDisplay(degreeMap, topEducation);
            educations.add(topEducation);
        }
        if(currentExperiences != null) {
            int size = currentExperiences.size();
            int i = 0;
            while((educations.size() + experiences.size()) < 4 && i < 3 && i < size) {
                JSONObject jsonObject = currentExperiences.getJSONObject(i);
                experiences.add(jsonObject);
                i++;
            }
        }
        if(nonTopEducation != null && !nonTopEducation.isEmpty() && (educations.size() + experiences.size()) < 4) {
            addDegreeDisplay(degreeMap, nonTopEducation.getJSONObject(0));
            educations.add(nonTopEducation.getJSONObject(0));
        }
        if(pastExperiences != null) {
            int size = pastExperiences.size();
            int i = 0;
            while((educations.size() + experiences.size()) < 4 && i < 2 && i < size) {
                experiences.add(pastExperiences.getJSONObject(i));
                i++;
            }
        }
    }

    private void addDegreeDisplay(Map<String, EnumDegree> degreeMap, JSONObject education) {
        String degreeLevel = education.getStr("degreeLevel");
        if(degreeLevel == null) {
            return;
        }
        EnumDegree enumDegree = degreeMap.get(degreeLevel);
        if(enumDegree == null) {
            return;
        }
        education.put("chineseDegreeName", enumDegree.getCnDisplay());
        education.put("englishDegreeName", enumDegree.getEnDisplay());
    }

    private JSONArray getCommonPoolSearchResult(List<String> talentIds, Map<String, CreditTransaction> map) throws JsonProcessingException {
        if(talentIds.isEmpty()) {
            return new JSONArray();
        }
        String result = null;
        try {
            result = esCommonService.searchTalentFromCommonService(getTalentSearchGroup(talentIds, true)).getBody();
        } catch (IOException e) {
            throw new RuntimeException(e);
        }

        Map<String, EnumLanguage> languageMap = enumCommonService.findAllEnumLanguages().stream().collect(Collectors.toMap(EnumLanguage::getName, e -> e));
        JSONArray resultArray = JSONUtil.parseArray(result);
        if (resultArray.size() > 0) {
            for (int i = 0; i < resultArray.size(); i++) {
                JSONObject searchData = resultArray.getJSONObject(i);
                String id = searchData.getStr("_id");
                CreditTransaction creditTransaction = map.get(id);

                JSONObject source = searchData.getJSONObject("_source");
                searchData.put("_unlockCandidate", creditTransaction == null);
                searchData.put("_appendToTalent", creditTransaction == null || creditTransaction.getTalentId() == null);
                searchData.put("_type", "COMMON_POOL_TALENT");

                JSONArray languages = source.getJSONArray("languages");
                if (languages != null) {
                    JSONArray newLanguages = new JSONArray();
                    for (int j = 0; j < languages.size(); j++) {
                        String name = String.valueOf(languages.get(j));
                        EnumLanguage enumLanguage = languageMap.get(name);
                        JSONObject record = new JSONObject();
                        if (enumLanguage != null) {
                            record.put("id", enumLanguage.getId());
                            record.put("name", enumLanguage.getName());
                            record.put("cn_display", enumLanguage.getCnDisplay());
                            record.put("en_display", enumLanguage.getEnDisplay());
                            record.put("cn_sort_order", enumLanguage.getCnDisplayOrder());
                            record.put("en_sort_order", enumLanguage.getEnDisplayOrder());
                        } else {
                            record.put("name", name);
                        }
                        newLanguages.add(record);
                    }
                    source.put("languages", newLanguages);
                }
                source.put("hasResume", false);
                if (creditTransaction != null) {
                    source.put("purchased", true);
                    Long talentId = creditTransaction.getTalentId();
                    if (talentId != null) {
                        source.put("talentId", talentId);
                    }
                } else {
                    source.put("purchased", false);
                }
                source.put("totalPhones", source.containsKey("totalPhones") ? 1 : 0);
                source.put("totalEmails", source.containsKey("emailStatus") ? 1 : 0);
                source.put("totalWechats", 0);
                source.put("totalWhatsApps", 0);
                source.put("totalLinkedIns", source.containsKey("formattedLinkedIn") ? 1 : 0);
            }
        }
        return resultArray;
    }

    private int getContactCount(ContactType contactType, List<TalentContactDTO> contacts) {
        if(contacts == null || contacts.isEmpty()) {
            return 0;
        }
        int count = 0;
        for(TalentContactDTO contact : contacts) {
            if(contactType.equals(contact.getType())) {
                count++;
            }
        }
        return count;
    }

    private SearchGroup getTalentSearchGroup(List<String> talentId, boolean common) {
        SearchGroup searchGroup = new SearchGroup();
        List<SearchParam> search = new ArrayList<>();
        SearchParam searchParam = new SearchParam();
        searchParam.setRelation(Relation.AND);
        List<ConditionParam> conditions = new ArrayList<>();
        ConditionParam conditionParam = new ConditionParam();
        conditionParam.setKey("candidateId");
        JSONObject param = new JSONObject();
        param.put("data", talentId);
        conditionParam.setValue(param);
        conditions.add(conditionParam);
        searchParam.setCondition(conditions);
        search.add(searchParam);
        searchGroup.setSearch(search);
        searchGroup.setModule(common ? "COMMON_POOL" : "TALENT_POOL");
        searchGroup.setLanguage("zh");
        searchGroup.setTimeZone("Asia/Shanghai");
        if(!common) {
            searchGroup.setIndex(com.altomni.apn.common.config.constants.Constants.INDEX_TALENTS + SecurityUtils.getTenantId());
        }
        SearchFilterDTO filterDTO = new SearchFilterDTO();
        ArrayList<String> source = new ArrayList<>(ElasticSearchConstants.RECOMMEND_TALENT_KEY);
        if(common) {
            source.add("emailStatus");
            source.add("totalPhones");
            source.add("formattedLinkedIn");
        }
        filterDTO.setSource(source);
        searchGroup.setFilter(filterDTO);
        return searchGroup;
    }

    @Resource
    private TalentService talentService;

    @Resource
    private ApplicationService applicationService;

    private JSONArray getTalentSearchResult(List<String> talentId, Long jobId) {
        if(talentId.isEmpty()) {
            return new JSONArray();
        }
        List<Long> talentIds = applicationService.getTalentRecruitmentProcessBriefByJobId(jobId).getBody().stream().filter(p -> {
            NodeType lastNodeType = p.getLastNodeType();
            NodeStatus lastNodeStatus = p.getLastNodeStatus();
            return !NodeType.ELIMINATED.equals(lastNodeType) && !NodeStatus.ELIMINATED.equals(lastNodeStatus);
        }).map(TalentRecruitmentProcessVO::getTalentId).toList();
        String result = null;
        try {
            result = esCommonService.searchTalentFromCommonService(getTalentSearchGroup(talentId, false)).getBody();
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        JSONArray resultArray = JSONUtil.parseArray(result);
        if (resultArray.size() > 0) {
            // 使用迭代器来安全地在循环中删除元素
            Iterator<Object> iterator = resultArray.iterator();
            while (iterator.hasNext()) {
                JSONObject searchData = (JSONObject) iterator.next();
                String id = searchData.getStr("_id");
                long lId = Long.parseLong(id);
                if(!talentService.hasTalentViewAuthority(lId) || !talentConfidentialService.confidentialTalentViewAble(lId)) {
                    // 如果没有查看权限，从结果中删除此条目
                    iterator.remove();
                    continue;  // 跳过此次循环的剩余部分
                }
                JSONObject source = searchData.getJSONObject("_source");
                searchData.put("_relateJobFolder", judgeAppendCurrentJob(source.getJSONArray("foldersOfPreSubmitTalents"), jobId));
                searchData.put("_appendToPosition", !talentIds.contains(lId));
                searchData.put("_type", "TENANT_TALENT");
            }
        }
        return resultArray;
    }

    private Object judgeAppendCurrentJob(JSONArray jsonArray, Long jobId) {
        if(jsonArray == null) {
            return true;
        }
        for (int i = 0; i < jsonArray.size(); i++) {
            JSONObject jsonObject = jsonArray.getJSONObject(i);
            if(jobId.equals(jsonObject.getLong("jobId"))) {
                return false;
            }
        }
        return true;
    }

    private void splitCommonAndTenantId(Set<String> talentIdSet, List<String> talentId, List<String> commonPoolEsId, Map<String, CreditTransaction> map, Map<String, String> talentId2EsId) {
        for(String id : talentIdSet) {
            if(NumberUtil.isNumber(id)) {
                talentId.add(id);
            } else {
                CreditTransaction creditTransaction = map.get(id);
                if(creditTransaction != null && creditTransaction.getTalentId() != null) {
                    talentId2EsId.put(creditTransaction.getTalentId().toString(), id);
                    talentId.add(creditTransaction.getTalentId().toString());
                } else {
                    commonPoolEsId.add(id);
                }
            }
        }
    }

    private Map<String, CreditTransaction> getCreditTransactionMap(List<String> searchEsIds) {
        if(searchEsIds.isEmpty()) {
            return new HashMap<>();
        }
        List<CreditTransaction> creditTransactions = userService.findAllCreditTransactionByTenantIdAndStatusAndSearchEsIdIn(SecurityUtils.getTenantId(), Status.Available, searchEsIds).getBody();
        if (creditTransactions == null) {
            creditTransactions = new ArrayList<>();
        }
        return creditTransactions.stream().collect(Collectors.toMap(CreditTransaction::getCommonDBSearchESId, a -> a));
    }


    private TalentSearchGroup addContactSearchDataPermission(TalentSearchGroup searchGroup) {
        TalentSearchGroup countSearchGroup = TalentSearchGroup.deepCopySearchGroup(searchGroup);
        if (SecurityUtils.isAdmin()) {
            return countSearchGroup;
        }


//        TeamDataPermissionRespDTO teamDataPermission = cachePermission.getTeamDataPermissionFromCacheOnly(SecurityUtils.getUserId());
//        if (Objects.isNull(teamDataPermission)) {
//
//        }
        TeamDataPermissionRespDTO teamDataPermission = initiationService.initiateClientContactDataPermissionByUserId(SecurityUtils.getTenantId(), SecurityUtils.getUserId()).getBody();
        if (teamDataPermission.getAll()) {
            return countSearchGroup;
        }

        Set<Long> teamIds = BooleanUtils.isTrue(teamDataPermission.getSelf()) ? Collections.emptySet() : teamDataPermission.getReadableTeamIds();
        List<String> affiliations = countSearchGroup.getFilter().getAffiliations();
        if(affiliations == null) {
            affiliations = new ArrayList<>();
        }
        affiliations.addAll(getContactOwnershipPermissionAffiliationFilter(teamIds));
        countSearchGroup.getFilter().setAffiliations(affiliations);
        return countSearchGroup;
    }


    private List<String> getContactOwnershipPermissionAffiliationFilter(Set<Long> teamIds) {
        List<String> affiliation = new ArrayList<>();

        //for all pure talent or share with all
        affiliation.add("all");

        //contact owned by user and user with team permission
        if (CollUtil.isNotEmpty(teamIds)) {
            List<String> teamStr = teamIds.stream()
                    .map(teamId -> "pteam_" + teamId)
                    .collect(Collectors.toList());
            affiliation.addAll(teamStr);
        }

        //self
        affiliation.add("puser_" + SecurityUtils.getUserId());

        return affiliation;
    }


    private JobFolderSearchFilter getRelateJobFolderTalent(List<ComplexSearchParam> searchParamList, List<String> folderIds, Boolean belongToRelateJobFolder) {
        if (folderIds == null || folderIds.isEmpty()) {
            throw new CustomParameterizedException("Please input folderIds.");
        }
        String folderId = folderIds.get(0);
        JobFolderSearchFilter searchFilter = new JobFolderSearchFilter();
        JobFolder jobFolder = new JobFolder();
        jobFolder.setFolderId(folderId);
        if (belongToRelateJobFolder == null) {
            belongToRelateJobFolder = true;
        }
        jobFolder.setBelongToThisFolder(belongToRelateJobFolder);
        searchFilter.setJobFolder(jobFolder);
        List<SearchParam> jobFolderSearchParam = new ArrayList<>();
        for (ComplexSearchParam searchParam : searchParamList) {
            List<ConditionParam> jobFolderConditionParam = new ArrayList<>();
            getRelateJobFolderTalentProcessSearchConditions(searchParam, jobFolderConditionParam);
            if (!jobFolderConditionParam.isEmpty()) {
                SearchParam specialSearchParam = new SearchParam();
                specialSearchParam.setRelation(searchParam.getRelation());
                specialSearchParam.setCondition(jobFolderConditionParam);
                jobFolderSearchParam.add(specialSearchParam);
            }
        }
        searchFilter.setSearch(jobFolderSearchParam);
        return searchFilter;
    }

    private boolean getRelateJobFolderTalentProcessSearchConditions(SearchCondition condition, List<ConditionParam> jobFolderConditionParam) {
        if (condition instanceof ComplexSearchParam) {
            ComplexSearchParam complexParam = (ComplexSearchParam) condition;
            // 处理复合条件
            if (complexParam.getCondition() != null) {
                Iterator<SearchCondition> iterator = complexParam.getCondition().iterator();
                while (iterator.hasNext()) {
                    SearchCondition subCondition = iterator.next();
                    boolean shouldRemove = getRelateJobFolderTalentProcessSearchConditions(subCondition, jobFolderConditionParam);
                    if (shouldRemove) {
                        iterator.remove();
                    }
                }
            }
            // 如果所有子条件都被删除，返回true表示当前复合条件也应该被删除
            return complexParam.getCondition() == null || complexParam.getCondition().isEmpty();
        } else if (condition instanceof ConditionParam) {
            ConditionParam conditionParam = (ConditionParam) condition;
            // 处理简单条件
            if (ResponsibilityConstants.ADDED_BY.equals(conditionParam.getKey())) {
                conditionParam.setKey("responsibility5.id");
                jobFolderConditionParam.add(conditionParam);
                return true; // 标记需要删除
            }
        }
        return false; // 不需要删除
    }

    @Resource
    private TalentRelateJobFolderRepository talentRelateJobFolderRepository;

    @Resource
    private TalentRelateJobFolderTalentRepository talentRelateJobFolderTalentRepository;

    private List<SearchParam> getRelateJobFolderFilter(List<SearchParam> filter, List<String> folderIds) {
        //TODO 临时从数据库中搜出职位文件夹候选人id当条件
        if (filter == null) {
            filter = new ArrayList<>();
        }
        if (folderIds == null || folderIds.isEmpty()) {
            return null;
        }
        String folderId = folderIds.get(0);
        List<TalentAssociationJobFolderTalent> folderTalents = talentRelateJobFolderTalentRepository.findByTalentRelateJobFolderFolderIdIs(folderId);
        if (folderTalents.isEmpty()) {
            return null;
        }
        List<String> talentIdList = folderTalents.stream().map(TalentAssociationJobFolderTalent::getTalentId).map(String::valueOf).collect(Collectors.toList());
        SearchParam belongFolder = new SearchParam();
        belongFolder.setRelation(Relation.AND);
        List<ConditionParam> conditionParams = new ArrayList<>();
        ConditionParam conditionParam = new ConditionParam();
        conditionParam.setKey("candidateId");
        JSONObject value = new JSONObject();
        value.put("data", talentIdList);
        value.put("relation", Relation.OR.name());
        conditionParam.setValue(value);
        conditionParams.add(conditionParam);
        belongFolder.setCondition(conditionParams);
        filter.add(belongFolder);
        return filter;
    }

    private String formatRelateJobFolderTalentData(String body, Pageable pageable, String folderId) {
        TeamDataPermissionRespDTO teamDataPermission = cachePermission.getTeamDataPermissionFromCacheOnly(SecurityUtils.getUserId());
        log.info("DataPermission (user: {}) = {}", SecurityUtils.getUserId(), teamDataPermission);
        if (Objects.isNull(teamDataPermission)) {
            teamDataPermission = initiationService.initiateDataPermissionByUserId(SecurityUtils.getTenantId(), SecurityUtils.getUserId()).getBody();
        }
        List<EnumUserResponsibility> applicationsEnumUserResponsibility = enumCommonService.findApplicationsEnumUserResponsibility();
        JSONArray resultArray = JSONUtil.parseArray(body);
        JSONArray formatArray = new JSONArray();
        if (resultArray.size() > 0) {
            for (int i = 0; i < resultArray.size(); i++) {
                JSONObject searchData = JSONUtil.parseObj(resultArray.get(i));
                String id = searchData.getStr("_id");
                JSONObject source = searchData.getJSONObject("_source");
                //个人权限不返回 pteam_相关
                if(teamDataPermission.getSelf()) {
                    JSONArray affiliations = source.getJSONArray("affiliations");
                    JSONArray newAffiliations = new JSONArray();
                    if(affiliations != null) {
                        for(int j = 0; j < affiliations.size(); j++) {
                            String str = affiliations.get(j, String.class);
                            if(!str.contains("pteam_")) {
                                newAffiliations.add(str);
                            }
                        }
                        source.put("affiliations", newAffiliations);
                    }
                }

                JSONArray foldersOfPreSubmitTalents = source.getJSONArray("foldersOfPreSubmitTalents");
                if (foldersOfPreSubmitTalents != null) {
                    JSONArray newFoldersOfPreSubmitTalents = new JSONArray();
                    for (int j = 0; j < foldersOfPreSubmitTalents.size(); j++) {
                        JSONObject folder = foldersOfPreSubmitTalents.getJSONObject(j);
                        if (folderId.equalsIgnoreCase(folder.getStr("folderId"))) {
                            newFoldersOfPreSubmitTalents.add(folder);
                        }
                    }
                    source.put("foldersOfPreSubmitTalents", newFoldersOfPreSubmitTalents);
                }

                JSONArray currentExperiences = source.getJSONArray("currentExperiences");
                if (currentExperiences != null) {
                    currentExperiences.sort(new Comparator<Object>() {
                        @Override
                        public int compare(Object o1, Object o2) {
                            JSONObject jsonObject1 = new JSONObject(o1);
                            JSONObject jsonObject2 = new JSONObject(o2);
                            String name1 = jsonObject1.getStr("companyNamePinYin");
                            if (name1 == null) {
                                name1 = "";
                            }
                            String name2 = jsonObject2.getStr("companyNamePinYin");
                            if (name2 == null) {
                                name2 = "";
                            }
                            return name1.compareTo(name2);
                        }
                    });
                    source.put("currentExperiences", currentExperiences);
                }

                List<String> applicationsResponsibility = new ArrayList<>(List.of(ResponsibilityConstants.AM,ResponsibilityConstants.COOPERATE_ACCOUNT_MANAGER, ResponsibilityConstants.DM, ResponsibilityConstants.RECRUITER));
                Sort.Direction searchSort = Sort.Direction.ASC;
                String sortKey = null;
                if (pageable.getSort().isSorted()) {
                    Iterator<Sort.Order> sorts = pageable.getSort().iterator();
                    while (sorts.hasNext()) {
                        Sort.Order sort = sorts.next();
                        if (applicationsResponsibility.contains(sort.getProperty())) {
                            searchSort = sort.getDirection();
                            sortKey = sort.getProperty();
                            break;
                        }
                    }
                }
                sortKey = getTranslateSortKey(sortKey, applicationsEnumUserResponsibility);
                if (sortKey != null) {
                    JSONArray applications = source.getJSONArray("applications");
                    if (applications != null) {
                        sortApplications(applications, sortKey, searchSort == Sort.Direction.ASC);
                        source.put("applications", applications);
                    }
                }
                JSONArray industries = source.getJSONArray("industries");
                if (industries != null) {
                    JSONArray newIndustries = new JSONArray();
                    for (int j = 0; j < industries.size(); j++) {
                        JSONObject industry = industries.getJSONObject(j);
                        industry.remove("name");
                        newIndustries.add(industry);
                    }
                    source.put("industries", newIndustries);
                }

                JSONArray jobFunctions = source.getJSONArray("jobFunctions");
                if (jobFunctions != null) {
                    JSONArray newJobFunctions = new JSONArray();
                    for (int j = 0; j < jobFunctions.size(); j++) {
                        JSONObject jobFunction = jobFunctions.getJSONObject(j);
                        jobFunction.remove("name");
                        newJobFunctions.add(jobFunction);
                    }
                    source.put("jobFunctions", newJobFunctions);
                }


                formatArray.add(searchData);
            }
        }
        return formatArray.toString();
    }

    private String formatTalentData(String body, Pageable pageable) {
        TeamDataPermissionRespDTO teamDataPermission = initiationService.initiateClientContactDataPermissionByUserId(SecurityUtils.getTenantId(), SecurityUtils.getUserId()).getBody();
        log.info("DataPermission (user: {}) = {}", SecurityUtils.getUserId(), teamDataPermission);
//        if (Objects.isNull(teamDataPermission)) {
//            teamDataPermission = initiationService.initiateDataPermissionByUserId(SecurityUtils.getTenantId(), SecurityUtils.getUserId()).getBody();
//        }

        List<EnumUserResponsibility> applicationsEnumUserResponsibility = enumCommonService.findApplicationsEnumUserResponsibility();

        JSONArray resultArray = JSONUtil.parseArray(body);
        JSONArray formatArray = new JSONArray();
        if (resultArray.size() > 0) {
            Set<Long> talentIds = new HashSet<>();
            boolean hasPermission = SecurityUtils.isAdmin()
                    || !ApplicationIPGProperties.IPG_RULE_TENANT_IDS.contains(SecurityUtils.getTenantId())
                    || cachePermission.hasUserPrivilegePermission(SecurityUtils.getUserId(), applicationProperties.getSubmitToJobPermissionKey());
            for (int i = 0; i < resultArray.size(); i++) {
                JSONObject searchData = JSONUtil.parseObj(resultArray.get(i));
                // 如果当前登录用户有提交在职候选人到新职位的权限，则该候选人在前端列表中不用置灰
                if (hasPermission){
                    searchData.put("isAvailableForSubmittingToJob", Boolean.TRUE);
                }
                String id = searchData.getStr("_id");
                talentIds.add(Long.parseLong(id));
                JSONObject source = searchData.getJSONObject("_source");
                //个人权限不返回 pteam_相关
                if(teamDataPermission.getSelf()) {
                    JSONArray affiliations = source.getJSONArray("affiliations");
                    JSONArray newAffiliations = new JSONArray();
                    if(affiliations != null) {
                        for(int j = 0; j < affiliations.size(); j++) {
                            String str = affiliations.get(j, String.class);
                            if(!str.contains("pteam_")) {
                                newAffiliations.add(str);
                            }
                        }
                        source.put("affiliations", newAffiliations);
                    }
                }


                JSONArray currentExperiences = source.getJSONArray("currentExperiences");
                if (currentExperiences != null) {
                    currentExperiences.sort(new Comparator<Object>() {
                        @Override
                        public int compare(Object o1, Object o2) {
                            JSONObject jsonObject1 = new JSONObject(o1);
                            JSONObject jsonObject2 = new JSONObject(o2);
                            String name1 = jsonObject1.getStr("companyNamePinYin");
                            if (name1 == null) {
                                name1 = "";
                            }
                            String name2 = jsonObject2.getStr("companyNamePinYin");
                            if (name2 == null) {
                                name2 = "";
                            }
                            return name1.compareTo(name2);
                        }
                    });
                    source.put("currentExperiences", currentExperiences);
                }

                List<String> applicationsResponsibility = new ArrayList<>(List.of(ResponsibilityConstants.AM,ResponsibilityConstants.COOPERATE_ACCOUNT_MANAGER, ResponsibilityConstants.DM, ResponsibilityConstants.RECRUITER));
                Sort.Direction searchSort = Sort.Direction.ASC;
                String sortKey = null;
                if (pageable.getSort().isSorted()) {
                    Iterator<Sort.Order> sorts = pageable.getSort().iterator();
                    while (sorts.hasNext()) {
                        Sort.Order sort = sorts.next();
                        if (applicationsResponsibility.contains(sort.getProperty())) {
                            searchSort = sort.getDirection();
                            sortKey = sort.getProperty();
                            break;
                        }
                    }
                }
                sortKey = getTranslateSortKey(sortKey, applicationsEnumUserResponsibility);
                if (sortKey != null) {
                    JSONArray applications = source.getJSONArray("applications");
                    if (applications != null) {
                        sortApplications(applications, sortKey, searchSort == Sort.Direction.ASC);
                        source.put("applications", applications);
                    }
                }
                JSONArray industries = source.getJSONArray("industries");
                if (industries != null) {
                    JSONArray newIndustries = new JSONArray();
                    for (int j = 0; j < industries.size(); j++) {
                        JSONObject industry = industries.getJSONObject(j);
                        industry.remove("name");
                        newIndustries.add(industry);
                    }
                    source.put("industries", newIndustries);
                }

                JSONArray jobFunctions = source.getJSONArray("jobFunctions");
                if (jobFunctions != null) {
                    JSONArray newJobFunctions = new JSONArray();
                    for (int j = 0; j < jobFunctions.size(); j++) {
                        JSONObject jobFunction = jobFunctions.getJSONObject(j);
                        jobFunction.remove("name");
                        newJobFunctions.add(jobFunction);
                    }
                    source.put("jobFunctions", newJobFunctions);
                }

                //客户联系人不再mask姓名信息
//                JSONArray affiliations = source.getJSONArray(ElasticSearchConstants.AFFILIATION_ES_KEY);
//                if (affiliations != null && !doesTalentViewableAsContact(source, SecurityUtils.getUserId())) {
//                    //maskTalentByPermission(source);
//                    source.put("maskedTalent", true);
//
//                } else {
//                    source.put("maskedTalent", false);
//
//                }

                formatArray.add(searchData);
            }

            // 如果当前登录用户没有提交在职候选人到新职位的权限，则需要去判断该候选人在前端列表中是否置灰
            if (BooleanUtils.isFalse(hasPermission)){
                JSONArray newFormatArray = new JSONArray();
                Set<Long> availableTalents = this.getAvailableTalentsForTalentSubmitToJob(talentIds);
                // 设置是否该候选人在前端置灰
                for (int i = 0; i < formatArray.size(); i++) {
                    JSONObject searchData = JSONUtil.parseObj(formatArray.get(i));
                    Long talentId = searchData.getLong("_id");
                    searchData.put("isAvailableForSubmittingToJob", availableTalents.contains(talentId));
                    newFormatArray.add(searchData);
                }
                return newFormatArray.toString();
            }
        }
        return formatArray.toString();
    }

    private Set<Long> getAvailableTalentsForTalentSubmitToJob(Set<Long> talentIds){
        Set<Long> unavailableTalentIds = financeService.filterTalentsWithActiveFteStarts(talentIds).getBody();
        return SetUtils.difference(talentIds, unavailableTalentIds);
    }

    private void maskTalentByPermission(JSONObject source) {
        Map<String, Object> modifications = new HashMap<>();


        Iterator<String> keys = source.keySet().iterator();
        while (keys.hasNext()) {
            String key = keys.next();
            // 排除 "fullName" 和 "responsibility" 开头
            if (!key.equals("fullName") && !key.startsWith("responsibility") && !key.equals("firstName") && !key.equals("lastName")) {
                Object value = source.get(key);
                if (value instanceof String) {
                    modifications.put(key, "*");
                } else if (value instanceof Number) {
                    modifications.put(key, (Number) null);
                } else if (value instanceof JSONArray) {
                    JSONArray array = (JSONArray) value;
                    JSONArray newArray = new JSONArray();
                    for (int i = 0; i < array.size(); i++) {
                        Object arrayElement = array.get(i);
                        if (arrayElement instanceof JSONObject) {
                            maskTalentByPermission((JSONObject) arrayElement);
                            newArray.add(arrayElement);
                        } else if (arrayElement instanceof Number) {
                            newArray.add((Number) null);
                        } else {
                            newArray.add("*");
                        }
                    }
                    modifications.put(key, newArray);
                } else if (value instanceof JSONObject) {
                    maskTalentByPermission((JSONObject) value);
                } else {
                    modifications.put(key, "*");
                }
            }
        }

        for (Map.Entry<String, Object> entry : modifications.entrySet()) {
            source.put(entry.getKey(), entry.getValue());
        }
    }

    private String getTranslateSortKey(String sortKey,List<EnumUserResponsibility> applicationsEnumUserResponsibility) {
        if (sortKey == null) {
            return null;
        }

        for (EnumUserResponsibility responsibility : applicationsEnumUserResponsibility) {
            if (responsibility.getLabel().equals(sortKey)) {
                return responsibility.getApplicationEsKey().split("\\.")[1];
            }
        }
        return null;
    }

    private void sortApplications(JSONArray jsonArray, String responsibility, boolean asc) {
        // 对responsibility5中的对象进行排序
        jsonArray.forEach(obj -> {
            JSONObject jsonObject = (JSONObject) obj;
            JSONArray resp5Array = jsonObject.getJSONArray(responsibility);
            if (resp5Array != null) {
                if (asc) {
                    resp5Array.sort(Comparator.comparing(o -> ((JSONObject) o).getStr("namePinYin")));
                } else {
                    resp5Array.sort(Comparator.comparing(o -> ((JSONObject) o).getStr("namePinYin")).reversed());
                }
            }
        });

        // 对最外层的数组中的对象进行排序
        jsonArray.sort((o1, o2) -> {
            JSONArray responsibility5 = ((JSONObject) o1).getJSONArray(responsibility);
            if (responsibility5 == null) {
                return 1;
            }
            JSONObject jo1 = responsibility5.getJSONObject(0);
            JSONArray responsibility51 = ((JSONObject) o2).getJSONArray(responsibility);
            if (responsibility51 == null) {
                return -1;
            }
            JSONObject jo2 = responsibility51.getJSONObject(0);
            if (jo1 != null && jo2 != null) {
                int com = jo1.getStr("namePinYin").compareTo(jo2.getStr("namePinYin"));
                return asc ? com : -com;
            }
            return 0;
        });
    }

    private void checkFoldersPermission(TalentSearchConditionDTO condition) {
        if (condition.getFolderIds() == null) {
            return;
        }
        if (condition.getModule() == ModuleType.RELATE_JOB_FOLDER) {
            return;
        }
        List<Long> lFolders = condition.getFolderIds().stream().map(Long::valueOf).collect(Collectors.toList());
        List<TalentFolder> folderList = talentFolderRepository.findAllByIdIn(lFolders);
        if (folderList.size() != lFolders.size()) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(TalentAPIMultilingualEnum.TALENT_CHECKFOLDERSPERMISSION_FOLDERNOTEXIST.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), talentApiPromptProperties.getTalentService()));
        }
        if (!checkSearchPermissionOnFolders(folderList)) {
            throw new CustomParameterizedException("No folder access permission.");
        }
    }

    private boolean checkSearchPermissionOnFolders(List<TalentFolder> talentFolders) {
        log.info("Check user read permission on folders: userId:{}, teamId: {}", SecurityUtils.getUserId(), SecurityUtils.getTeamId());
        List<Long> notOwnFolderIds = talentFolders.stream()
                .filter(folder -> !folder.getPermissionUserId().equals(SecurityUtils.getUserId()))
                .map(TalentFolder::getId)
                .collect(Collectors.toList());
        if (notOwnFolderIds.size() > 0) {
            List<TalentFolderSharingUser> shareToUserList = talentFolderSharingUserRepository.findAllByTalentFolderIdInAndUserId(notOwnFolderIds, SecurityUtils.getUserId());
            shareToUserList.stream().filter(TalentFolderSharingUser::hasReadPermission).forEach(t -> {
                notOwnFolderIds.remove(t.getTalentFolderId());
            });
            if (CollUtil.isNotEmpty(notOwnFolderIds)) {
                List<TalentFolderSharingTeam> shareToTeamList = talentFolderSharingTeamRepository.findAllByTalentFolderIdInAndTeamId(notOwnFolderIds, SecurityUtils.getTeamId());
                shareToTeamList.stream().filter(TalentFolderSharingTeam::hasReadPermission).forEach(t -> {
                    TalentFolderSharingTeamDTO talentFolderSharingTeamDTO = talentFolderSharingTeamMapper.toDto(t);
                    if (!talentFolderSharingTeamDTO.DoesUserRemoveTeamSharing(SecurityUtils.getUserId())) {
                        notOwnFolderIds.remove(t.getTalentFolderId());
                    }
                });
            }
        }
        return notOwnFolderIds.size() <= 0;
    }

    private String formatCommonPoolData(String body) {
        JSONArray resultArray = JSONUtil.parseArray(body);
        JSONArray formatArray = new JSONArray();
        if (resultArray.size() > 0) {
            List<String> searchEsIds = getSearchEsIds(resultArray);
            List<CreditTransaction> creditTransactions = userService.findAllCreditTransactionByTenantIdAndStatusAndSearchEsIdIn(SecurityUtils.getTenantId(), Status.Available, searchEsIds).getBody();
            if (creditTransactions == null) {
                creditTransactions = new ArrayList<>();
            }
//            Map<String, EnumIndustry> industryMap = enumCommonService.findAllEnumIndustry().stream().collect(Collectors.toMap(EnumIndustry::getName, e -> e));
            Map<String, EnumLanguage> languageMap = enumCommonService.findAllEnumLanguages().stream().collect(Collectors.toMap(EnumLanguage::getName, e -> e));
            //commonPool老数据 map关系在代码中，等commonPool更新后会直接返回id
            Map<String, Long> jobFunctionMap = getCommonPoolMap();
            Map<String, CreditTransaction> map = creditTransactions.stream().collect(Collectors.toMap(CreditTransaction::getCommonDBSearchESId, a -> a));
            for (int i = 0; i < resultArray.size(); i++) {
                JSONObject searchData = JSONUtil.parseObj(resultArray.get(i));
                String id = searchData.getStr("_id");
                CreditTransaction creditTransaction = map.get(id);
                JSONObject source = searchData.getJSONObject("_source");
                if (creditTransaction != null) {
                    source.put("purchased", true);
                    Long talentId = creditTransaction.getTalentId();
                    if (talentId != null) {
                        source.put("talentId", talentId);
                    }
                    //set LinkedIn Link
//                    if (ObjectUtil.isNotEmpty(creditTransaction.getEsContacts())) {
//                        List<TalentContact> contacts = JSONUtil.toList(JSONUtil.parseArray(creditTransaction.getEsContacts()), TalentContact.class);
//                        if (ObjectUtil.isNotEmpty(contacts)) {
//                            Optional<TalentContact> contact = contacts.stream().filter(c -> c.getType().equals(ContactType.LINKEDIN)).findFirst();
//                            contact.ifPresent(talentContact -> source.put("LinkedInLink", talentContact.getDetails()));
//                        }
//                    }
                } else {
                    source.put("purchased", false);
                }
                //fix language/industries/jobFunctions
                JSONArray languages = source.getJSONArray("languages");
                if (languages != null) {
                    JSONArray newLanguages = new JSONArray();
                    for (int j = 0; j < languages.size(); j++) {
                        String name = String.valueOf(languages.get(j));
                        EnumLanguage enumLanguage = languageMap.get(name);
                        JSONObject record = new JSONObject();
                        if (enumLanguage != null) {
                            record.put("id", enumLanguage.getId());
                            record.put("name", enumLanguage.getName());
                            record.put("cn_sort_order", enumLanguage.getCnDisplayOrder());
                            record.put("en_sort_order", enumLanguage.getEnDisplayOrder());
                        } else {
                            record.put("name", name);
                        }
                        newLanguages.add(record);
                    }
                    source.put("languages", newLanguages);
                }


                // DO NOT convert industries

//                JSONArray industries = source.getJSONArray("industries");
//                if (industries != null) {
//                    JSONArray newIndustries = new JSONArray();
//                    for (int j = 0; j < industries.size(); j++) {
//                        String name = String.valueOf(industries.get(j));
//                        EnumIndustry enumIndustry = industryMap.get(name);
//                        JSONObject record = new JSONObject();
//                        if (enumIndustry != null) {
//                            record.put("id", enumIndustry.getId());
////                            record.put("name", enumIndustry.getName());
//                            record.put("cn_sort_order", enumIndustry.getCnDisplayOrder());
//                            record.put("en_sort_order", enumIndustry.getEnDisplayOrder());
//                        } else {
//                            record.put("name", name);
//                        }
//                        newIndustries.add(record);
//                    }
//                    source.put("industries", newIndustries);
//                }

                // DO NOT convert jobFunctions

//                JSONArray jobFunctions = source.getJSONArray("jobFunctions");
//                if (jobFunctions != null) {
//                    JSONArray newJobFunctions = new JSONArray();
//                    for (int j = 0; j < jobFunctions.size(); j++) {
//                        String name = String.valueOf(jobFunctions.get(j));
//                        Long enumId = jobFunctionMap.get(name);
//                        JSONObject record = new JSONObject();
//                        if (enumId != null) {
//                            record.put("id", enumId);
//                            record.put("cn_sort_order", enumId);
//                            record.put("en_sort_order", enumId);
//                        }
//                        newJobFunctions.add(record);
//                    }
//                    source.put("jobFunctions", newJobFunctions);
//                }

                formatArray.add(searchData);
            }
        }
        return formatArray.toString();
    }

    public static Map<String, Long> getCommonPoolMap() {
        Map<String, Long> commonPoolMap = new HashMap<>();
        commonPoolMap.put("OPERATIONS.PRODUCT_MANAGER", 3L);
        commonPoolMap.put("MARKETING.MARKETING_PRODUCT_MANAGER", 4L);
        commonPoolMap.put("ANALYTICS.BUSINESS_INTELLIGENCE", 6L);
        commonPoolMap.put("OPERATIONS.PROJECT_MANAGER", 7L);
        commonPoolMap.put("OPERATIONS.TECHNICAL_PROJECT_MANAGER", 8L);
        commonPoolMap.put("ARTS_DESIGN_ENTERTAINMENT_SPORTS_AND_MEDIA.UI_OR_UX.UI", 12L);
        commonPoolMap.put("ARTS_DESIGN_ENTERTAINMENT_SPORTS_AND_MEDIA.UI_OR_UX.UX", 13L);
        commonPoolMap.put("ARTS_DESIGN_ENTERTAINMENT_SPORTS_AND_MEDIA.UI_OR_UX", 14L);
        commonPoolMap.put("INSURANCE", 15L);
        commonPoolMap.put("ARTIFICIAL_INTELLIGENCE", 16L);
        commonPoolMap.put("BIOLOGY.BIOENGINEERING", 17L);
        commonPoolMap.put("CHEMISTRY.CHEMICAL_ENGINEERING", 18L);
        commonPoolMap.put("SOFTWARE.BACKEND.CLOUD", 19L);
        commonPoolMap.put("ANALYTICS.DATA_SCIENCE", 20L);
        commonPoolMap.put("SOFTWARE.MOBILE", 23L);
        commonPoolMap.put("SOFTWARE.BACKEND.SDET/QA", 26L);
        commonPoolMap.put("HARDWARE.MECHANICAL_ENGINEER", 28L);
        commonPoolMap.put("SOFTWARE.INFORMATION_SECURITY", 29L);
        commonPoolMap.put("SOFTWARE.IT_SUPPORT", 30L);
        commonPoolMap.put("FINANCE", 32L);
        commonPoolMap.put("BIOLOGY.BIOMETRICS", 33L);
        commonPoolMap.put("LEGAL", 34L);
        commonPoolMap.put("MARKETING", 35L);
        commonPoolMap.put("OPERATIONS", 36L);
        commonPoolMap.put("SALES", 37L);
        commonPoolMap.put("SOFTWARE.DEVOPS_OR_SRE", 38L);
        commonPoolMap.put("TRADE", 41L);
        commonPoolMap.put("EDUCATION", 42L);
        commonPoolMap.put("ARCHITECTURE_AND_ENGINEERING.INDUSTRIAL_ENGINEERING", 43L);
        commonPoolMap.put("SOFTWARE.BACKEND.DATA_ENGINEER", 54L);
        commonPoolMap.put("SOFTWARE.BACKEND.DATABASE", 55L);
        commonPoolMap.put("SOFTWARE.SYSTEM_ADMIN", 57L);
        commonPoolMap.put("SOFTWARE.BACKEND.INFRASTRUCTURE", 60L);
        commonPoolMap.put("FINANCE.COMPLIANCE", 75L);
        commonPoolMap.put("FINANCE.FINANCIAL_REPORTING", 77L);
        commonPoolMap.put("FINANCE.FINANCIAL_STRATEGY", 78L);
        commonPoolMap.put("FINANCE.FINANCIAL_SYSTEMS", 79L);
        commonPoolMap.put("FINANCE.INTERNAL_AUDIT_AND_CONTROL", 80L);
        commonPoolMap.put("FINANCE.INVESTOR_RELATIONS", 81L);
        commonPoolMap.put("FINANCE.MERGERS_AND_ACQUISITIONS", 82L);
        commonPoolMap.put("FINANCE.RISK", 84L);
        commonPoolMap.put("FINANCE.SHARED_SERVICES", 85L);
        commonPoolMap.put("TRADE.SOURCING_OR_PROCUREMENT", 86L);
        commonPoolMap.put("FINANCE.TAX", 87L);
        commonPoolMap.put("FINANCE.TREASURY", 88L);
        commonPoolMap.put("HUMAN_RESOURCES.DIVERSITY_AND_INCLUSION", 90L);
        commonPoolMap.put("SOFTWARE", 91L);
        commonPoolMap.put("HUMAN_RESOURCES.HEALTH_AND_SAFETY", 92L);
        commonPoolMap.put("HUMAN_RESOURCES.TALENT_MANAGEMENT", 97L);
        commonPoolMap.put("HUMAN_RESOURCES.WORKFORCE_MANAGEMENT", 98L);
        commonPoolMap.put("LEGAL.ACQUISITIONS", 99L);
        commonPoolMap.put("LEGAL.COMPLIANCE", 100L);
        commonPoolMap.put("LEGAL.CONTRACTS", 101L);
        commonPoolMap.put("LEGAL.CORPORATE_SECRETARY", 102L);
        commonPoolMap.put("LEGAL.EDISCOVERY", 103L);
        commonPoolMap.put("LEGAL.ETHICS", 104L);
        commonPoolMap.put("LEGAL.GOVERNANCE", 105L);
        commonPoolMap.put("LEGAL.GOVERNMENTAL_AFFAIRS_AND_REGULATORY_LAW", 106L);
        commonPoolMap.put("LEGAL.INTELLECTUAL_PROPERTY_AND_PATENT", 107L);
        commonPoolMap.put("LEGAL.LABOR_AND_EMPLOYMENT", 108L);
        commonPoolMap.put("LEGAL.LAWYER_OR_ATTORNEY", 109L);
        commonPoolMap.put("LEGAL.LEGAL_COUNSEL", 110L);
        commonPoolMap.put("LEGAL.LEGAL_OPERATIONS", 111L);
        commonPoolMap.put("LEGAL.LITIGATION", 112L);
        commonPoolMap.put("LEGAL.PRIVACY", 113L);
        commonPoolMap.put("TRADE.SUPPLIER_DEVELOPMENT", 114L);
        commonPoolMap.put("TRADE.FOREIGN_TRADE", 115L);
        commonPoolMap.put("TRADE.DOMESTIC_TRADE", 116L);
        commonPoolMap.put("TRADE.ORDER_SUPERVISOR", 117L);
        commonPoolMap.put("OPERATIONS.CALL_CENTER", 118L);
        commonPoolMap.put("OPERATIONS.CONSTRUCTION", 119L);
        commonPoolMap.put("OPERATIONS.CUSTOMER_SERVICE_OR_SUPPORT", 121L);
        commonPoolMap.put("OPERATIONS.ENTERPRISE_RESOURCE_PLANNING", 122L);
        commonPoolMap.put("OPERATIONS.FACILITIES_MANAGEMENT", 123L);
        commonPoolMap.put("OPERATIONS.LEASING", 124L);
        commonPoolMap.put("OPERATIONS.PHYSICAL_SECURITY", 127L);
        commonPoolMap.put("OPERATIONS.QUALITY_MANAGEMENT", 128L);
        commonPoolMap.put("OPERATIONS.SAFETY", 129L);
        commonPoolMap.put("OPERATIONS.STORE_OPERATIONS", 130L);
        commonPoolMap.put("OPERATIONS.SUPPLY_CHAIN", 131L);
        commonPoolMap.put("OPERATIONS.OPERATION", 134L);
        commonPoolMap.put("SALES.ACCOUNT_MANAGEMENT", 135L);
        commonPoolMap.put("SALES.BUSINESS_DEVELOPMENT", 136L);
        commonPoolMap.put("SALES.CHANNEL_SALES", 137L);
        commonPoolMap.put("SALES.CUSTOMER_RETENTION_AND_DEVELOPMENT", 138L);
        commonPoolMap.put("SALES.CUSTOMER_SUCCESS", 139L);
        commonPoolMap.put("SALES.FIELD_OR_OUTSIDE_SALES", 140L);
        commonPoolMap.put("SALES.INSIDE_SALES", 141L);
        commonPoolMap.put("SALES.PARTNERSHIPS", 142L);
        commonPoolMap.put("SALES.REVENUE_OPERATIONS", 143L);
        commonPoolMap.put("SALES.SALES_ENABLEMENT", 144L);
        commonPoolMap.put("SALES.SALES_ENGINEERING", 145L);
        commonPoolMap.put("SALES.SALES_OPERATIONS", 146L);
        commonPoolMap.put("SALES.SALES_TRAINING", 147L);
        commonPoolMap.put("CHEMISTRY.CMC", 149L);
        commonPoolMap.put("MARKETING.ADVERTISING", 152L);
        commonPoolMap.put("MEDICAL_AND_HEALTH.CLINICAL.CLINICAL_OPERATION_PROJECT_MANAGEMENT", 155L);
        commonPoolMap.put("MEDICAL_AND_HEALTH.CLINICAL.CLINICAL_QUALITY", 156L);
        commonPoolMap.put("MEDICAL_AND_HEALTH.CLINICAL.CLINICAL_RESEARCH_PHYSICIAN", 159L);
        commonPoolMap.put("MEDICAL_AND_HEALTH.CLINICAL.PHARMACOVIGILANCE", 161L);
        commonPoolMap.put("MEDICAL_AND_HEALTH", 163L);
        commonPoolMap.put("MEDICAL_AND_HEALTH.REGULATORY_AFFAIRS", 164L);
        commonPoolMap.put("BIOLOGY.BIOLOGY_SCIENCE", 165L);
        commonPoolMap.put("CHEMISTRY.CHEMICAL_SCIENCE", 166L);
        commonPoolMap.put("MEDICAL_AND_HEALTH.REGULATORY_AFFAIRS.NEW_DRUG", 167L);
        commonPoolMap.put("MEDICAL_AND_HEALTH.REGULATORY_AFFAIRS.GENERIC_DRUG", 168L);
        commonPoolMap.put("MEDICAL_AND_HEALTH.PRE_CLINICAL.TOXICOLOGY", 169L);
        commonPoolMap.put("MEDICAL_AND_HEALTH.PRE_CLINICAL.PHARMACOLOGY", 170L);
        commonPoolMap.put("MEDICAL_AND_HEALTH.PRE_CLINICAL", 171L);
        commonPoolMap.put("RESEARCH.MATERIAL", 172L);
        commonPoolMap.put("RESEARCH.FIRMWARE", 173L);
        commonPoolMap.put("RESEARCH.SOFTWARE", 174L);
        commonPoolMap.put("RESEARCH.ARTIFICIAL_INTELLIGENCE", 175L);
        commonPoolMap.put("RESEARCH.FINANCE", 176L);
        commonPoolMap.put("RESEARCH.LEGAL", 177L);
        commonPoolMap.put("RESEARCH", 178L);
        commonPoolMap.put("MEDICAL_AND_HEALTH.REGULATORY_AFFAIRS.INTERNATIONAL", 179L);
        commonPoolMap.put("HARDWARE.ELECTRICAL_ENGINEER", 180L);
        commonPoolMap.put("MEDICAL_AND_HEALTH.REGULATORY_AFFAIRS.CMC_RA", 181L);
        commonPoolMap.put("FINANCE.ACCOUNTING", 182L);
        commonPoolMap.put("FINANCE.FINANCIAL_PLANNING_AND_ANALYSIS", 183L);
        commonPoolMap.put("FINANCE.REAL_ESTATE", 184L);
        commonPoolMap.put("OPERATIONS.OFFICE_OPERATIONS", 185L);
        commonPoolMap.put("OPERATIONS.CORPORATE_STRATEGY", 186L);
        commonPoolMap.put("OPERATIONS.LOGISTICS", 187L);
        commonPoolMap.put("OPERATIONS.REAL_ESTATE_DESIGN", 188L);
        commonPoolMap.put("OPERATIONS.REAL_ESTATE_PROPERTY_MANAGEMENT", 189L);
        commonPoolMap.put("ARTS_DESIGN_ENTERTAINMENT_SPORTS_AND_MEDIA.GRAPHIC_DESIGN", 190L);
        commonPoolMap.put("ARTS_DESIGN_ENTERTAINMENT_SPORTS_AND_MEDIA.EDITING", 191L);
        commonPoolMap.put("HUMAN_RESOURCES.COMPENSATION_AND_BENEFITS.COMPENSATION", 193L);
        commonPoolMap.put("HUMAN_RESOURCES.COMPENSATION_AND_BENEFITS.BENEFIT", 194L);
        commonPoolMap.put("HUMAN_RESOURCES.COMPENSATION_AND_BENEFITS.PERFORMANCE", 195L);
        commonPoolMap.put("HUMAN_RESOURCES.COMPENSATION_AND_BENEFITS.LONG_TERM_INCENTIVE", 196L);
        commonPoolMap.put("HUMAN_RESOURCES.COMPENSATION_AND_BENEFITS", 197L);
        commonPoolMap.put("HUMAN_RESOURCES.EMPLOYEE_AND_LABOR_RELATIONS", 198L);
        commonPoolMap.put("HUMAN_RESOURCES.MIDDLE_PLATFORM.INFORMATION_SYSTEM", 200L);
        commonPoolMap.put("HUMAN_RESOURCES.MIDDLE_PLATFORM.BUSINESS_INTELLIGENCE", 201L);
        commonPoolMap.put("HUMAN_RESOURCES.MIDDLE_PLATFORM.POLICY_PROCESS", 202L);
        commonPoolMap.put("HUMAN_RESOURCES.MIDDLE_PLATFORM", 203L);
        commonPoolMap.put("HUMAN_RESOURCES.LEARNING_AND_DEVELOPMENT", 204L);
        commonPoolMap.put("HUMAN_RESOURCES.ORGANIZATIONAL_DEVELOPMENT.TALENT_DEVELOPMENT", 206L);
        commonPoolMap.put("HUMAN_RESOURCES.ORGANIZATIONAL_DEVELOPMENT.ORGANIZATION_DEVELOPMENT", 207L);
        commonPoolMap.put("HUMAN_RESOURCES.ORGANIZATIONAL_DEVELOPMENT.ORGANIZATION_CULTURE", 208L);
        commonPoolMap.put("HUMAN_RESOURCES.ORGANIZATIONAL_DEVELOPMENT", 209L);
        commonPoolMap.put("HUMAN_RESOURCES.RECRUITING_AND_TALENT_ACQUISITION.CAMPUS_RECRUITMENT", 211L);
        commonPoolMap.put("HUMAN_RESOURCES.RECRUITING_AND_TALENT_ACQUISITION.EMPLOYER_BRANDING", 212L);
        commonPoolMap.put("HUMAN_RESOURCES.RECRUITING_AND_TALENT_ACQUISITION.RECRUITMENT_DELIVER", 213L);
        commonPoolMap.put("HUMAN_RESOURCES.RECRUITING_AND_TALENT_ACQUISITION.RECRUITMENT_OPERATION", 214L);
        commonPoolMap.put("HUMAN_RESOURCES.RECRUITING_AND_TALENT_ACQUISITION", 215L);
        commonPoolMap.put("HUMAN_RESOURCES.GENERALIST_HRBP", 216L);
        commonPoolMap.put("HUMAN_RESOURCES.SHARED_SERVICE_CENTER", 217L);
        commonPoolMap.put("HUMAN_RESOURCES", 218L);
        commonPoolMap.put("BIOLOGY.BIOINFORMATION", 223L);
        commonPoolMap.put("MEDICAL_AND_HEALTH.CLINICAL.MEDICAL_WRITING", 232L);
        commonPoolMap.put("MEDICAL_AND_HEALTH.CLINICAL.CLINICAL_OPERATION_PEOPLE_MANAGEMENT", 233L);
        commonPoolMap.put("MEDICAL_AND_HEALTH.CLINICAL.MSL", 235L);
        commonPoolMap.put("MEDICAL_AND_HEALTH.CLINICAL.MA", 236L);
        commonPoolMap.put("MEDICAL_AND_HEALTH.CLINICAL.NGS", 237L);
        commonPoolMap.put("MEDICAL_AND_HEALTH.INDICATION.SOLID_TUMOR", 239L);
        commonPoolMap.put("MEDICAL_AND_HEALTH.INDICATION.HEMATOLOGY", 240L);
        commonPoolMap.put("MEDICAL_AND_HEALTH.INDICATION.CV", 241L);
        commonPoolMap.put("MEDICAL_AND_HEALTH.INDICATION.INCRETION", 242L);
        commonPoolMap.put("MEDICAL_AND_HEALTH.INDICATION.IMMUNOLOGY", 243L);
        commonPoolMap.put("MEDICAL_AND_HEALTH.INDICATION.CNS", 244L);
        commonPoolMap.put("MEDICAL_AND_HEALTH.INDICATION.BONE", 245L);
        commonPoolMap.put("MEDICAL_AND_HEALTH.INDICATION.INFECTION", 246L);
        commonPoolMap.put("MEDICAL_AND_HEALTH.INDICATION.RARE_DISEASE", 247L);
        commonPoolMap.put("MEDICAL_AND_HEALTH.INDICATION.DERMATOLOGY", 248L);
        commonPoolMap.put("MEDICAL_AND_HEALTH.CELL_THERAPY", 249L);
        commonPoolMap.put("MEDICAL_AND_HEALTH.GENE_EDITING", 250L);
        commonPoolMap.put("MEDICAL_AND_HEALTH.LNP_DELIVERING", 251L);
        commonPoolMap.put("MEDICAL_AND_HEALTH.MEDICINE_OR_MEDICAL_EQUIPMENT_MANUFACTURING", 252L);
        commonPoolMap.put("MEDICAL_AND_HEALTH.PRE_CLINICAL.PKPD", 256L);
        commonPoolMap.put("MEDICAL_AND_HEALTH.PRE_CLINICAL.BIOANALYSIS", 257L);
        commonPoolMap.put("MEDICAL_AND_HEALTH.PRE_CLINICAL.BIOMARKER", 258L);
        commonPoolMap.put("BIOLOGY", 264L);
        commonPoolMap.put("CHEMISTRY", 265L);
        commonPoolMap.put("MEDICAL_AND_HEALTH.CLINICAL", 266L);
        commonPoolMap.put("MEDICAL_AND_HEALTH.INDICATION", 267L);
        commonPoolMap.put("MEDICAL_AND_HEALTH.FIRST_RESPONDER", 311L);
        commonPoolMap.put("MEDICAL_AND_HEALTH.OPHTHALMOLOGY", 312L);
        commonPoolMap.put("MARKETING.BRAND_MANAGEMENT", 323L);
        commonPoolMap.put("MARKETING.CONTENT_MARKETING", 324L);
        commonPoolMap.put("MARKETING.CUSTOMER_MARKETING", 325L);
        commonPoolMap.put("MARKETING.DEMAND_GENERATION", 326L);
        commonPoolMap.put("MARKETING.DIGITAL_MARKETING", 327L);
        commonPoolMap.put("MARKETING.ECOMMERCE", 328L);
        commonPoolMap.put("MARKETING.EVENT_MARKETING", 329L);
        commonPoolMap.put("MARKETING.FIELD_MARKETING", 330L);
        commonPoolMap.put("MARKETING.LEAD_GENERATION", 332L);
        commonPoolMap.put("MARKETING.MARKETING_ANALYTICS_OR_INSIGHTS", 333L);
        commonPoolMap.put("MARKETING.MARKETING_COMMUNICATIONS", 334L);
        commonPoolMap.put("MARKETING.MARKETING_OPERATIONS", 335L);
        commonPoolMap.put("MARKETING.PUBLIC_RELATIONS_PR", 336L);
        commonPoolMap.put("MARKETING.REVENUE_OPERATIONS", 337L);
        commonPoolMap.put("MARKETING.SEARCH_ENGINE_OPTIMIZATION_OR_PAY_PER_CLICK", 338L);
        commonPoolMap.put("MARKETING.SOCIAL_MEDIA_MARKETING", 339L);
        commonPoolMap.put("MARKETING.STRATEGIC_COMMUNICATIONS", 340L);
        commonPoolMap.put("MARKETING.TECHNICAL_MARKETING", 341L);
        commonPoolMap.put("MEDICAL_AND_HEALTH.ANESTHESIOLOGY", 342L);
        commonPoolMap.put("MEDICAL_AND_HEALTH.CHIROPRACTICS", 344L);
        commonPoolMap.put("MEDICAL_AND_HEALTH.CLINICAL_SYSTEMS", 346L);
        commonPoolMap.put("MEDICAL_AND_HEALTH.DENTISTRY", 347L);
        commonPoolMap.put("MEDICAL_AND_HEALTH.MEDICAL_ADMINISTRATION", 353L);
        commonPoolMap.put("MEDICAL_AND_HEALTH.MEDICAL_EDUCATION_AND_TRAINING", 354L);
        commonPoolMap.put("MEDICAL_AND_HEALTH.NURSING", 357L);
        commonPoolMap.put("MEDICAL_AND_HEALTH.NUTRITION_AND_DIETETICS", 358L);
        commonPoolMap.put("MEDICAL_AND_HEALTH.OBSTETRICS_OR_GYNECOLOGY", 359L);
        commonPoolMap.put("MEDICAL_AND_HEALTH.ONCOLOGY", 360L);
        commonPoolMap.put("MEDICAL_AND_HEALTH.OPTOMETRY", 362L);
        commonPoolMap.put("MEDICAL_AND_HEALTH.ORTHOPEDICS", 363L);
        commonPoolMap.put("MEDICAL_AND_HEALTH.PATHOLOGY", 364L);
        commonPoolMap.put("MEDICAL_AND_HEALTH.PEDIATRICS", 365L);
        commonPoolMap.put("MEDICAL_AND_HEALTH.PHYSICAL_THERAPY", 367L);
        commonPoolMap.put("MEDICAL_AND_HEALTH.PSYCHIATRY", 368L);
        commonPoolMap.put("MEDICAL_AND_HEALTH.PSYCHOLOGY", 369L);
        commonPoolMap.put("MEDICAL_AND_HEALTH.PUBLIC_HEALTH", 370L);
        commonPoolMap.put("MEDICAL_AND_HEALTH.RADIOLOGY", 371L);
        commonPoolMap.put("MEDICAL_AND_HEALTH.SOCIAL_WORK", 372L);
        commonPoolMap.put("MEDICAL_AND_HEALTH.SURGERY", 373L);
        commonPoolMap.put("SOFTWARE.WEB_FRONTEND.ANGULAR", 374L);
        commonPoolMap.put("SOFTWARE.WEB_FRONTEND.REACT", 375L);
        commonPoolMap.put("SOFTWARE.WEB_FRONTEND", 376L);
        commonPoolMap.put("SOFTWARE.BACKEND", 379L);
        commonPoolMap.put("SOFTWARE.BACKEND.FIRMWARE_OR_EMBEDDED", 380L);
        commonPoolMap.put("SOFTWARE.BACKEND.OPERATING_SYSTEM", 381L);
        commonPoolMap.put("SOFTWARE.BACKEND.APPLIED_MACHINE_LEARNING", 382L);
        commonPoolMap.put("SOFTWARE.BACKEND.BLOCKCHAIN", 383L);
        commonPoolMap.put("SOFTWARE.FULLSTACK", 384L);
        commonPoolMap.put("SOFTWARE.SYSTEM_ENGINEER", 385L);
        commonPoolMap.put("ANALYTICS", 386L);
        commonPoolMap.put("ANALYTICS.DATA_ANALYST", 387L);
        commonPoolMap.put("HARDWARE", 388L);
        commonPoolMap.put("HARDWARE.TEST_ENGINEER_SENSORS_OR_LIDAR", 389L);
        commonPoolMap.put("HARDWARE.TECHNICIAN", 390L);
        commonPoolMap.put("HARDWARE.FPGA", 391L);
        commonPoolMap.put("HARDWARE.ASIC", 392L);
        commonPoolMap.put("HARDWARE.ASIC.ANALOG_DESIGN", 393L);
        commonPoolMap.put("HARDWARE.ASIC.DIGITAL_DESIGN", 394L);
        commonPoolMap.put("HARDWARE.CPU_OR_GPU_ARCHITECT", 395L);
        commonPoolMap.put("HARDWARE.IMAGE_OR_AUDIO_SYSTEM_ARCHITECT", 396L);
        commonPoolMap.put("HARDWARE.IMAGE_OR_AUDIO_ALGORITHM", 397L);
        commonPoolMap.put("HARDWARE.VERIFICATION_OR_VALIDATION_OR_BRINGUP", 398L);
        commonPoolMap.put("HARDWARE.PACKAGING", 399L);
        commonPoolMap.put("HARDWARE.IMAGE_OR_ISP_OR_CAMERA_SYSTEMS", 400L);
        commonPoolMap.put("ARTIFICIAL_INTELLIGENCE.COMPUTER_VISION", 401L);
        commonPoolMap.put("ARTIFICIAL_INTELLIGENCE.AUTONOMOUS_DRIVING_OR_SLAM", 402L);
        commonPoolMap.put("ARTIFICIAL_INTELLIGENCE.RANKING_OR_RECOMMENDATION_OR_SEARCH_ALGORITHM", 403L);
        commonPoolMap.put("ARTIFICIAL_INTELLIGENCE.REINFORCEMENT_LEARNING", 404L);
        commonPoolMap.put("ARTIFICIAL_INTELLIGENCE.NLP.SPEECH", 405L);
        commonPoolMap.put("ARTIFICIAL_INTELLIGENCE.NLP.TEXT", 406L);
        commonPoolMap.put("ARTIFICIAL_INTELLIGENCE.AUDIO_SPEECH_RECOGNITION", 407L);
        commonPoolMap.put("ARCHITECTURE_AND_ENGINEERING", 408L);
        commonPoolMap.put("ARTS_DESIGN_ENTERTAINMENT_SPORTS_AND_MEDIA", 409L);
        commonPoolMap.put("ARTIFICIAL_INTELLIGENCE.NLP", 410L);
        commonPoolMap.put("OPERATIONS.MANAGEMENT_CONSULTING", 411L);
        return commonPoolMap;
    }

    private List<String> getSearchEsIds(JSONArray resultArray) {
        return resultArray.stream().map(r -> {
            return String.valueOf(JSONUtil.parseObj(r).get("_id"));
        }).collect(Collectors.toList());
    }

    private TalentColumnConfigDTO getColumnConfig(ModuleType module) {
        ResponseEntity<TalentColumnConfigDTO> getTalentColumnReq = null;
        if (ModuleType.COMMON_POOL.equals(module)) {
            getTalentColumnReq = userService.getTalentCommonPoolColumn();
        } else if (ModuleType.RELATE_JOB_FOLDER.equals(module)) {
            getTalentColumnReq = userService.getTalentRelateJobFolderColumnConfig();
        } else if (ModuleType.SEARCH_FOLDER.equals(module)) {
            getTalentColumnReq = userService.getTalentSearchFolderColumnConfig();
        } else {
            getTalentColumnReq = userService.getTalentColumn();
        }
        if (getTalentColumnReq.getStatusCode() != HttpStatus.OK) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(TalentAPIMultilingualEnum.TALENT_SEARCHTALENTFROMES_INTERFACEERROR.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), talentApiPromptProperties.getTalentService()));
        }
        return getTalentColumnReq.getBody();
    }

    private List<String> filterSearchSource(TalentColumnConfigDTO columnPreferencesConfig, ModuleType module) {
        if (columnPreferencesConfig == null) {
            return new ArrayList<>();
        }
        Set<String> source = new HashSet<>();
        UserCustomConfig userCustomConfig = columnPreferencesConfig.getCustomConfig();
        if (userCustomConfig == null) {
            return new ArrayList<>();
        }
        List<EnumUserResponsibility> talentEnumUserResponsibility = enumCommonService.findTalentEnumUserResponsibility();
        List<EnumUserResponsibility> applicationsEnumUserResponsibility = enumCommonService.findApplicationsEnumUserResponsibility();

        List<CustomColumnField> columnConfig = userCustomConfig.getColumnConfig();
        columnConfig.forEach(c -> {
            Optional<Boolean> active = Optional.of(c.isActive());
            if (active.get()) {
                TalentPreferenceColumnConfig.parse(c.getField()).ifPresent(t -> {
                    source.addAll(getUserResponsibilityTransferEsName(t, talentEnumUserResponsibility, applicationsEnumUserResponsibility));
                });
                if (TalentPreferenceColumnConfig.EXPERIENCE_YEARS.getConfigName().equals(c.getField())) {
                    source.add(ElasticSearchConstants.YEARS_SOURCE_PREFIX + LocalDate.now().getYear());
                }
            }
        });
        if (ModuleType.COMMON_POOL.equals(module)) {
            source.addAll(TalentPreferenceColumnConfig.COMMON_POOL_RETURN.getEsName());
        } else if (ModuleType.RELATE_JOB_FOLDER.equals(module)) {
            source.addAll(TalentPreferenceColumnConfig.RELATE_JOB_FOLDER_RETURN.getEsName());
        }
        //data view permission for client contact talent
        source.addAll(TalentPreferenceColumnConfig.CONTACT_DATA_PERMISSION_RETURN.getEsName());

        source.addAll(TalentPreferenceColumnConfig.DEFAULT_RETURN.getEsName());
        source.addAll(TalentPreferenceColumnConfig.CONFIDENTIAL_INFO_RETURN.getEsName());
        return new ArrayList<>(source);
    }

    private Collection<String> getUserResponsibilityTransferEsName(TalentPreferenceColumnConfig t, List<EnumUserResponsibility> talentEnumUserResponsibility, List<EnumUserResponsibility> applicationsEnumUserResponsibility) {
        List<String> ret = new ArrayList<>();
        String configName = t.getConfigName();
        talentEnumUserResponsibility.forEach(ac -> {
            if (ac.getLabel().equals(configName)) {
                ret.add(ac.getTalentEsKey());
            }
        });
        applicationsEnumUserResponsibility.forEach(ac -> {
            if (ac.getLabel().equals(configName)) {
                ret.add(ac.getApplicationEsKey());
            }
        });
        ret.addAll(t.getEsName());
        return ret.stream().distinct().collect(Collectors.toList());
    }

//    private void formatSearchGroup(List<SearchParam> search) {
//        search.forEach(s -> {
//            s.getCondition().forEach(l -> {
//                //translate dictCode
//                if (ElasticSearchConstants.DROP_DOWN_KEYS.contains(l.getKey())) {
//                    cn.hutool.json.JSONObject data = JSONUtil.parseObj(JSONUtil.toJsonStr(JSONUtil.parse(l.getValue())));
//                    Iterator iter = data.entrySet().iterator();
//                    while (iter.hasNext()) {
//                        Map.Entry entry = (Map.Entry) iter.next();
//                        if ("data".contains(entry.getKey().toString())) {
//                            switch (l.getKey()) {
//                                //jobFunction
//                                case ElasticSearchConstants.DROP_DOWN_JOBFUNCTION:
//                                    List<String> list = Convert.toList(String.class, entry.getValue());
//                                    entry.setValue(list.stream().map(Long::valueOf).toList());
//                                    break;
//                                //industry
//                                case ElasticSearchConstants.DROP_DOWN_INDUSTRY:
//                                    entry.setValue(enumIndustryService.getIndustriesByIds(Convert.toList(String.class, entry.getValue())));
//                                    break;
//                            }
//                            l.setValue(data);
//                        }
//                    }
//                }
//            });
//        });
//    }

    private void formatSearchGroup(List<ComplexSearchParam> search) {
        search.forEach(s -> {
            if (s.getCondition() != null) {
                processSearchConditions(s.getCondition());
            }
        });
    }

    private void processSearchConditions(List<SearchCondition> conditions) {
        conditions.forEach(condition -> {
            if (condition instanceof ComplexSearchParam) {
                ComplexSearchParam complexParam = (ComplexSearchParam) condition;
                if (complexParam.getCondition() != null) {
                    processSearchConditions(complexParam.getCondition());
                }
            } else if (condition instanceof ConditionParam) {
                ConditionParam conditionParam = (ConditionParam) condition;
                formatConditionParam(conditionParam);
            }
        });
    }

    private void formatConditionParam(ConditionParam conditionParam) {
        if (ElasticSearchConstants.DROP_DOWN_KEYS.contains(conditionParam.getKey())) {
            cn.hutool.json.JSONObject data = JSONUtil.parseObj(JSONUtil.toJsonStr(JSONUtil.parse(conditionParam.getValue())));
            Iterator<Map.Entry<String, Object>> iter = data.entrySet().iterator();

            while (iter.hasNext()) {
                Map.Entry<String, Object> entry = iter.next();
                if ("data".contains(entry.getKey())) {
                    switch (conditionParam.getKey()) {
                        case ElasticSearchConstants.DROP_DOWN_JOBFUNCTION:
                            List<String> list = Convert.toList(String.class, entry.getValue());
                            entry.setValue(list.stream().map(Long::valueOf).toList());
                            break;
                        case ElasticSearchConstants.DROP_DOWN_INDUSTRY:
                            entry.setValue(enumIndustryService.getIndustriesByIds(Convert.toList(String.class, entry.getValue())));
                            break;
                    }
                    conditionParam.setValue(data);
                }
            }
        }
    }

    /****
     * save value of general text from conditionDTO as history after successful search
     * @param condition
     */
    private void saveSearchHistory(SearchConditionDTO condition) {
        try {
            Long userId = SecurityUtils.getUserId();
            List<ConditionParam> searchParams = condition.getSearch() == null ? new ArrayList<ConditionParam>() :
                    condition.getSearch().stream()
                            .flatMap(search -> search.getCondition().stream())
                            .filter(param -> param.getKey().equals(GENERALTEXT))
                            .collect(Collectors.toList());
            List<ConditionParam> filterParams = condition.getFilter() == null ? new ArrayList<ConditionParam>() :
                    condition.getFilter().stream()
                            .flatMap(search -> search.getCondition().stream())
                            .filter(param -> param.getKey().equals(GENERALTEXT))
                            .collect(Collectors.toList());

            List<ConditionParam> params = Stream.concat(searchParams.stream(), filterParams.stream()).collect(Collectors.toList());
            if (params.size() > 0) {

                List<String> searchStrs = new ArrayList<>();

                for (ConditionParam conditionParam : params) {
                    try {
                        Object value = conditionParam.getValue();
                        JSONObject jsonObject = JSONUtil.parseObj(value);
                        Object data = jsonObject.get("data");
                        if (data instanceof String) {
                            String dataStr = (String) data;
                            searchStrs.add(dataStr);
                        } else if (data instanceof List) {
                            List<?> list = (List<?>) data;
                            if (!list.isEmpty() && list.get(0) instanceof String) {
                                searchStrs.addAll((List<String>) list);
                            }
                        }
                    } catch (IllegalArgumentException e) {
                        // Handle error: valueObject cannot be converted into a JsonNode
                        e.printStackTrace();
                        log.error("[APN] SaveSearchHisotry: valueObject cannot be converted into a JsonNode ");
                    }

                }
                CompletableFuture.supplyAsync(() -> {
                    commonRedisService.lPushStringListWithMaxSizeAndExpire(String.format(DATA_KEY_TALENT_SEARCH_HISTORY, userId), searchStrs, applicationProperties.getSearchHistoryMaxSize(), applicationProperties.getSearchHistoryExpireTime());
                    return 0;
                });

            }
        } catch (Exception ex) {
            log.error("[APN] Fail to save keyword history by user {}: condition: {}, error: {}", SecurityUtils.getUserId(), condition.toString(), ex);
        }

    }

    @Override
    public List<String> querySearchHistory() {
        List<String> searchHistoryList = commonRedisService.getListData(String.format(DATA_KEY_TALENT_SEARCH_HISTORY, SecurityUtils.getUserId()), 0, applicationProperties.getSearchHistoryMaxSize());
        return searchHistoryList;
    }

    /**
     * Get count of each talent search category(all talent, my talent, common-pool).
     *
     * @return List of JobStatusCountDTO
     */
    @Override
    public List<CategoryCountDTO> getTalentSearchCategoryStatistics() {
        TalentCategoryCountRequestDTO requestDto = new TalentCategoryCountRequestDTO(SecurityUtils.getUserId(), "talents_" + SecurityUtils.getTenantId());
        List<CategoryCountDTO> categoryCountDTOList = new ArrayList<>();
        Map<String, Long> categoryMap = Collections.emptyMap();
        try {
            categoryMap = esCommonService.getTalentCategoryCount(requestDto);
        } catch (IOException ex) {
            log.error("[APN: TalentESService] fetch talent Category count IO exception {}", ex);
        } catch (Exception ex) {
            log.error("[APN: TalentESService] fetch talent Category count exception {}", ex);
        }

        for (Map.Entry<String, Long> entry : categoryMap.entrySet()) {
            TalentSearchCategory category = TalentSearchCategory.fromESKey(entry.getKey());
            CategoryCountDTO dto = new CategoryCountDTO(category.getDbValue(), entry.getValue(), category.name());
            categoryCountDTOList.add(dto);
        }

        Collections.sort(categoryCountDTOList, Comparator.comparing(CategoryCountDTO::getCategoryId));
        return categoryCountDTOList;
    }

    @Override
    public String searchTalentSourceFromES(SearchTalentSourceInput condition, Pageable pageable, HttpHeaders headers) throws IOException {
        //check search area
//        SearchConditionDTO.checkPageable(pageable);
        SearchEsBySource searchEsBySource = new SearchEsBySource();
        searchEsBySource.setModule(ModuleType.CANDIDATE.getName());
        searchEsBySource.setIndex(com.altomni.apn.common.config.constants.Constants.INDEX_TALENTS + SecurityUtils.getTenantId());
        List<String> ids = condition.getIds();
        if (CollectionUtils.isEmpty(ids)) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(TalentAPIMultilingualEnum.TALENT_SEARCHTALENTSOURCEFROMES_INPUTEMPTY.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), talentApiPromptProperties.getTalentService()));
        }
        searchEsBySource.setIds(ids.stream().map(i -> {
            SearchEsId id = new SearchEsId();
            id.setId(Integer.parseInt(i));
            return id;
        }).collect(Collectors.toList()));
        //topEducation/nonTopEducations/currentExperiences/pastExperiences/notes
        searchEsBySource.setSource(transferToEsSource(condition.getSource()));

        HttpResponse response = esCommonService.searchTalentSourceFromCommonService(searchEsBySource, pageable);
        if (response.getCode() != HttpStatus.OK.value()) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(TalentAPIMultilingualEnum.TALENT_SEARCHTALENTFROMES_INTERFACEERROR.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), talentApiPromptProperties.getTalentService()));
        }
        if (ObjectUtil.isEmpty(response) || ObjectUtil.isEmpty(response.getBody())) {
            headers.set("Pagination-Count", String.valueOf(0));
            return "";
        } else {
            headers.set("Pagination-Count", ElasticSearchUtil.getObjectCountFromResponseHeader(response.getHeaders()) + "");
            return response.getBody();
        }
    }

    @Resource
    private JobTalentRecommendFeedbackRepository feedbackRepository;

    @Override
    public void recordTalentJobRecommend(RecommendFeedback dto) {
        JobTalentRecommendFeedback jobTalentRecommendFeedback = new JobTalentRecommendFeedback();
        jobTalentRecommendFeedback.setJobId(dto.getJobId());
        jobTalentRecommendFeedback.setTalentId(dto.getTalentId());
        RecommendFeedbackReason reason = dto.getReason();
        if(reason != null) {
            jobTalentRecommendFeedback.setReason(reason.name());
        }
        jobTalentRecommendFeedback.setTenantId(SecurityUtils.getTenantId());
        feedbackRepository.save(jobTalentRecommendFeedback);
    }

    private List<String> transferToEsSource(List<String> sources) {
        List<String> ret = new ArrayList<>();
        for (String source : sources) {
            Optional<SearchTalentEsSource> parse = SearchTalentEsSource.parse(source);
            if (parse.isEmpty()) {
                continue;
            }
            ret.addAll(parse.get().getEsSourceName());
        }

        return ret;
    }
}
