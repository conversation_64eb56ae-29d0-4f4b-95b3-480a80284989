package com.altomni.apn.report.repository;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.Pair;
import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.altomni.apn.common.domain.enumeration.company.AccountCompanyStatus;
import com.altomni.apn.common.domain.enumeration.job.JobType;
import com.altomni.apn.common.dto.permission.TeamDataPermissionRespDTO;
import com.altomni.apn.common.dto.recruiting.RecruitingKpiReportSearchDto;
import com.altomni.apn.common.dto.recruiting.SearchUserDto;
import com.altomni.apn.common.enumeration.recruiting.RecruitingKpiDateType;
import com.altomni.apn.common.enumeration.recruiting.RecruitingKpiGroupByFieldType;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.common.vo.recruiting.*;
import com.altomni.apn.report.domain.enumeration.ReportTableType;
import com.altomni.apn.report.domain.vo.CrmUserIdAndTeamIdVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Repository;
import org.springframework.util.StopWatch;

import javax.persistence.Query;
import java.time.DayOfWeek;
import java.time.Instant;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.temporal.TemporalAdjusters;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Slf4j
@Repository
public class RecruitingKpiCompanyRepository extends RecruitingKpiBaseRepository {

    public List<? extends RecruitingKpiCommonCountVO> searchJobKpiByCompany(RecruitingKpiReportSearchDto searchDto) {
        boolean jobDetailFlag = searchDto.getGroupByFieldList().contains(RecruitingKpiGroupByFieldType.JOB);
        if (BooleanUtil.isFalse(jobDetailFlag)) {
            return searchJobKpi(searchDto);
        } else {
            return searchJobDetailKpi(searchDto);
        }
    }

    public List<RecruitingKpiCommonCountVO> searchTalentKpiByCompany(RecruitingKpiReportSearchDto searchDto) {
        boolean jobDetailFlag = searchDto.getGroupByFieldList().contains(RecruitingKpiGroupByFieldType.JOB);
        return searchTalentKpi(searchDto, jobDetailFlag);
    }

    public List<? extends RecruitingKpiCommonCountVO> searchApplicationKpiByCompany(RecruitingKpiReportSearchDto searchDto, ReportTableType reportTableType) {
        boolean jobDetailFlag = searchDto.getGroupByFieldList().contains(RecruitingKpiGroupByFieldType.JOB);
        return searchApplicationKpiBy(searchDto, reportTableType, jobDetailFlag);
    }

    public List<RecruitingKpiCommonCountVO> searchJobNoteKpiByCompany(RecruitingKpiReportSearchDto searchDto) {
        boolean jobDetailFlag = searchDto.getGroupByFieldList().contains(RecruitingKpiGroupByFieldType.JOB);
        return searchJobNoteKpi(searchDto, jobDetailFlag);
    }

    public List<RecruitingKpiCommonCountVO> searchJobNoteKpi(RecruitingKpiReportSearchDto searchDto, boolean jobDetailFlag) {
        StopWatch stopWatch = new StopWatch("searchJobNotesTask jobDetail = " + jobDetailFlag);
        stopWatch.start();
        String sql = """
                SELECT
                	{selectFields}
                	count( DISTINCT jn.id ) count_num
                FROM
                	job_note jn
                	INNER JOIN job j ON j.id = jn.job_id
                	INNER JOIN company c ON c.id = j.company_id
                	inner join company_user_relation ul on ul.company_id = c.id
                    INNER JOIN permission_user_team put ON put.user_id = ul.user_id AND put.is_primary = 1
                    inner join permission_team pt on pt.id = put.team_id
                	inner join user u on u.id = ul.user_id
                	{joinTables}
                WHERE
                    j.tenant_id = :tenantId and jn.visible = 1
                	{whereClause}
                    {groupByField}
                """;
        Map<String, Object> param = new HashMap<>(16);
        param.put("tenantId", searchDto.getSearchTenantId());
        Map<String, String> map = new HashMap<>(16);
        map.put("selectFields", getSelectFieldsByCompany(searchDto, null));
        map.put("joinTables", getJoinTablesForJobNoteByCompany(searchDto));
        map.put("whereClause", jobDetailFlag? getWhereClauseForJobNoteWithJobDetailByCompany(searchDto, param): getWhereClauseForJobNoteByCompany(searchDto, param));
        map.put("groupByField", getGroupByField(searchDto.getGroupByFieldList()));
        List<RecruitingKpiCommonCountVO> voList = getClassList(sql, map, param, RecruitingKpiCommonCountVO.class);
        stopWatch.stop();
        log.info("[apn @{}] search job notes count task, jobDetail = {}, times = {}ms", searchDto.getSearchUserId(), jobDetailFlag, stopWatch.getTotalTimeMillis());
        return voList;
    }

    private List<? extends RecruitingKpiCommonCountVO> searchApplicationKpiBy(RecruitingKpiReportSearchDto searchDto, ReportTableType reportTableType, boolean jobDetailFlag) {
        StopWatch stopWatch = new StopWatch("searchApplicationTask jobDetail = " + jobDetailFlag);
        stopWatch.start();
        String sql;
        Class clazz;
        if (reportTableType == ReportTableType.INTERVIEW) {
            clazz = RecruitingKpiInterviewCountVO.class;
            sql = """
                WITH interview_max as (
                select
                id,
                max(progress) progress,
                talent_recruitment_process_id
                from talent_recruitment_process_interview
                group by talent_recruitment_process_id
                )
                SELECT
                	{selectFields}
                	count( DISTINCT CASE WHEN node.progress = 1 THEN node.id END ) AS interview1,
                	count( DISTINCT CASE WHEN node.progress = 2 THEN node.id END ) AS interview2,
                    count( DISTINCT CASE WHEN node.progress = 1 and max.progress = 1 and trpn.id is not null THEN node.id END ) AS current_interview1,
                    count( DISTINCT CASE WHEN node.progress = 2 and max.progress = 2 and trpn.id is not null THEN node.id END ) AS current_interview2,
                    count(DISTINCT CASE WHEN node.final_round = 1 THEN node.id END ) AS interview_final,
                    count( DISTINCT CASE WHEN node.final_round = 1 and trpn.id is not null THEN node.id END ) AS current_interview_final,
                	count( DISTINCT node.id ) interview_total,
                	count( DISTINCT CASE WHEN trpn.id is not null THEN node.id END ) AS current_interview_total,
                	count(DISTINCT node.talent_recruitment_process_id ) interview_total_process,
                	count(DISTINCT CASE WHEN trpn.id IS NOT NULL THEN node.talent_recruitment_process_id END ) AS current_interview_total_process,
                	count(distinct  case when trp.ai_score is not null then node.id else null end ) as interviewTotalAiRecommendNum,
                	count(distinct  case when trp.ai_score is not null and trpn.id IS NOT NULL then node.id else null end ) as currentInterviewTotalAiRecommendNum,
                	
                	count(distinct  case when trp.ai_score is not null then trp.id else null end ) as interviewTotalProcessAiRecommendNum,
                	count(distinct  case when trp.ai_score is not null and trpn.id IS NOT NULL then trp.id else null end ) as currentInterviewTotalProcessAiRecommendNum,
                	
                	count(DISTINCT CASE WHEN node.progress = 1 and trp.ai_score is not null THEN trp.id else null END ) AS interview1AiRecommendNum,
                	count(DISTINCT CASE WHEN node.progress = 1 and max.progress = 1 and trpn.id IS NOT NULL and trp.ai_score is not null THEN trp.id else null END ) AS currentInterview1AiRecommendNum,
                	count(DISTINCT CASE WHEN node.progress = 2 and trp.ai_score is not null THEN trp.id else null END  ) AS interview2AiRecommendNum,
                	count(DISTINCT CASE WHEN node.progress = 2 and max.progress = 2 and trpn.id IS NOT NULL and trp.ai_score is not null THEN trp.id else null END ) AS currentInterview2AiRecommendNum,
                	count(DISTINCT CASE WHEN node.final_round = 1 and trp.ai_score is not null THEN trp.id else null END ) AS interviewFinalAiRecommendNum,
                	count(DISTINCT CASE WHEN node.final_round = 1 and trpn.id IS NOT NULL and trp.ai_score is not null THEN trp.id else null END ) AS currentInterviewFinalAiRecommendNum,
                	
                	count(distinct  case when rf.id is not null then node.id else null end ) as interviewTotalPrecisionAiRecommendNum,
                	count(distinct  case when rf.id is not null and trpn.id IS NOT NULL then node.id else null end ) as currentInterviewTotalPrecisionAiRecommendNum,
                	count(distinct  case when rf.id is not null then trp.id else null end ) as interviewNumProcessPrecisionAIRecommend,
                	count(distinct  case when rf.id is not null and trpn.id IS NOT NULL then trp.id else null end ) as currentInterviewNumProcessPrecisionAIRecommend,
                	
                	count(DISTINCT CASE WHEN node.progress = 1 and rf.id is not null THEN node.id else null END ) AS interview1PrecisionAiRecommendNum,
                	count(DISTINCT CASE WHEN node.progress = 1 and max.progress = 1 and trpn.id IS NOT NULL and rf.id is not null THEN node.id else null END ) AS currentInterview1PrecisionAiRecommendNum,
                	count(DISTINCT CASE WHEN node.progress = 2 and rf.id is not null THEN node.id else null END  ) AS interview2PrecisionAiRecommendNum,
                	count(DISTINCT CASE WHEN node.progress = 2 and max.progress = 2 and trpn.id IS NOT NULL and rf.id is not null THEN node.id else null END ) AS currentInterview2PrecisionAiRecommendNum,
                	count(DISTINCT CASE WHEN node.final_round = 1 and rf.id is not null THEN node.id else null END ) AS interviewFinalPrecisionAiRecommendNum,
                	count(DISTINCT CASE WHEN node.final_round = 1 and trpn.id IS NOT NULL and rf.id is not null THEN node.id else null END ) AS currentInterviewFinalPrecisionAiRecommendNum
                	
                FROM
                	talent_recruitment_process_interview node
                	INNER JOIN talent_recruitment_process trp ON trp.id = node.talent_recruitment_process_id
                	left join (select distinct case when reason='UNLOCK_CANDIDATE' || reason='ADD_TO_TALENT' then ct.talent_id else rf.talent_id end as talent_id,rf.id,rf.job_id
                                 from job_talent_recommend_feedback rf\s
                                left join credit_transaction ct on ct.profile_id = rf.talent_id
                                where  rf.reason in('ADD_TO_POSITION','ADD_TO_ASSOCIATION_JOB_FOLDER','UNLOCK_CANDIDATE','ADD_TO_TALENT')) rf on rf.job_id = trp.job_id and rf.talent_id = trp.talent_id 
                	INNER JOIN job j ON j.id = trp.job_id
                	INNER JOIN company c ON c.id = j.company_id
                	INNER JOIN company_user_relation ul ON ul.company_id = c.id
                	inner join permission_user_team put on put.user_id = ul.user_id and put.is_primary = 1
                	inner join permission_team pt on pt.id = put.team_id
                	inner join user u on u.id = ul.user_id
                	{joinTables}
                    left join interview_max max on max.talent_recruitment_process_id = node.talent_recruitment_process_id
                WHERE
                	trp.tenant_id = :tenantId
                	{whereClause}
                    {groupByField}
                """;
        } else {
            clazz = RecruitingKpiApplicationCommonCountVO.class;
            sql = """
                    SELECT
                    	{selectFields}
                    	group_concat(distinct cir.industry_id) industries,
                    	count( DISTINCT node.id ) count_num,
                    	count(distinct  case when trp.ai_score is not null then trp.id else null end ) as aiRecommendNum,
                    	count(distinct  case when rf.id is not null then trp.id else null end ) as precisionAiRecommendNum
                    FROM
                    	{fromTable} node
                    	INNER JOIN talent_recruitment_process trp ON trp.id = node.talent_recruitment_process_id
                    	left join (select distinct case when reason='UNLOCK_CANDIDATE' || reason='ADD_TO_TALENT' then ct.talent_id else rf.talent_id end as talent_id,rf.id,rf.job_id
                                 from job_talent_recommend_feedback rf\s
                                left join credit_transaction ct on ct.profile_id = rf.talent_id
                                where  rf.reason in('ADD_TO_POSITION','ADD_TO_ASSOCIATION_JOB_FOLDER','UNLOCK_CANDIDATE','ADD_TO_TALENT')) rf on rf.job_id = trp.job_id and rf.talent_id = trp.talent_id 
                    	INNER JOIN job j ON j.id = trp.job_id
                    	INNER JOIN company c ON c.id = j.company_id
                    	INNER JOIN company_user_relation ul ON ul.company_id = c.id
                    	inner join permission_user_team put on put.user_id = ul.user_id AND put.is_primary = 1
                    	inner join permission_team pt on pt.id = put.team_id
                    	inner join user u on u.id = ul.user_id
                    	left join company_industry_relation cir on cir.company_id = c.id
                    	{joinTables}
                    WHERE
                        trp.tenant_id = :tenantId
                    	{whereClause}
                        {groupByField}
                    """;
        }
        Map<String, Object> param = new HashMap<>(16);
        param.put("tenantId", searchDto.getSearchTenantId());
        Map<String, String> map = new HashMap<>(16);
        map.put("fromTable", getFromTable(reportTableType));
        map.put("selectFields", getSelectFieldsByCompany(searchDto, reportTableType));
        map.put("joinTables", getJoinTablesForApplicationByCompany(searchDto, reportTableType));
        map.put("whereClause", jobDetailFlag? getWhereClauseForApplicationWithJobDetailByCompany(searchDto, param, reportTableType): getWhereClauseForApplicationByCompany(searchDto, param, reportTableType));
        map.put("groupByField", getGroupByField(searchDto.getGroupByFieldList()));
        List<RecruitingKpiCommonCountVO> voList = getClassList(sql, map, param, clazz);
        stopWatch.stop();
        log.info("[apn @{}] search {} count task, jobDetail = {}, times = {}ms", searchDto.getSearchUserId(), reportTableType, jobDetailFlag, stopWatch.getTotalTimeMillis());
        return voList;
    }

    private List<RecruitingKpiJobDetailCommonCountVO> searchJobDetailKpi(RecruitingKpiReportSearchDto searchDto) {
        StopWatch stopWatch = new StopWatch("searchJobDetailTask");
        stopWatch.start();
        String sql = """
                SELECT
                	j.id job_id,
                	{selectFields}
                	j.title job_title,
                	j.pteam_id,
                	c.id company_id,
                	c.full_business_name company_name,
                	j.openings count_num,
                	GROUP_CONCAT( DISTINCT concat( t.id, "-", t.full_name )) contacts,
                	group_concat( DISTINCT concat( u.id, "-", u.first_name, " ", u.last_name )) assigned_user,
                	j.STATUS job_status,
                	j.start_date_format job_start_date,
                	j.contract_duration contract_duration,
                	j.end_date_format job_end_date,
                	j.currency job_currency,
                	jai.extended_info ->> '$.salaryRange.gte' AS minimum_pay_rate,
                	jai.extended_info ->> '$.salaryRange.lte' AS maximum_pay_rate,
                	if(j.sales_lead_id is null, null, if(ab.business_progress = 60, "Master Contract", "Trial Case")) job_cooperation_status
                FROM
                	company c
                	inner join job j on c.id = j.company_id
                	LEFT JOIN job_company_contact_relation jccr ON jccr.job_id = j.id
                	LEFT JOIN company_sales_lead_client_contact cslcc ON cslcc.id = jccr.client_contact_id
                	LEFT JOIN talent t ON t.id = cslcc.talent_id
                    LEFT JOIN company_user_relation ul ON ul.company_id = c.id
                	LEFT JOIN user_job_relation ujr ON ujr.job_id = j.id AND ujr.status = 1
                	LEFT JOIN USER u ON u.id = ujr.user_id
                	LEFT JOIN job_additional_info jai ON jai.id = j.additional_info_id
                	LEFT JOIN talent_recruitment_process trp ON trp.job_id = j.id
                	inner join permission_user_team put on put.user_id = ul.user_id and put.is_primary = 1
                    inner join permission_team pt on pt.id = put.team_id
                	LEFT JOIN USER uu ON uu.id = put.user_id
                	left join account_business ab on ab.id = j.sales_lead_id
                	{joinTables}
                WHERE
                    j.tenant_id = :tenantId
                	{whereClause}
                	{groupBy}
                """;
        Map<String, String> map = new HashMap<>(16);
        Map<String, Object> param = new HashMap<>(16);
        param.put("tenantId", searchDto.getSearchTenantId());
        map.put("selectFields", getSelectFieldsForJob(searchDto.getGroupByFieldList()));
        map.put("companyField", getCompanyField());
        map.put("companyTable", getCompanyTable());
        map.put("joinTables", getJoinTablesForJobByCompany(searchDto));
        StringBuilder whereClauseForJobForCompany = getWhereClauseForJobForCompany(searchDto, param);
        this.appendUserActiveStatus("uu", whereClauseForJobForCompany, searchDto.getUser(), param);
        map.put("whereClause", whereClauseForJobForCompany.toString());
        map.put("groupBy", getGroupByField(searchDto.getGroupByFieldList()));
        List<RecruitingKpiJobDetailCommonCountVO> voList = getClassList(sql, map, param, RecruitingKpiJobDetailCommonCountVO.class);
        stopWatch.stop();
        voList.forEach(vo -> {
            if (searchDto.getPermissionRespDTO() != null
                    && Objects.equals(searchDto.getPermissionRespDTO().getTeamIdForPrivateJob(), vo.getPteamId())) {
                vo.setPrivateJob(true);
            }
        });
        log.info("[apn @{}] search job detail count task, times = {}ms", searchDto.getSearchUserId(), stopWatch.getTotalTimeMillis());
        return voList;
    }

    public List<RecruitingKpiJobDetailCommonCountVO> searchJobDetailTwoKpi(RecruitingKpiReportSearchDto searchDto) {
        StopWatch stopWatch = new StopWatch("searchJobDetailTask");
        stopWatch.start();
        String sql = """
                SELECT
                	j.id job_id,
                	{selectFields}
                	j.title job_title,
                	j.pteam_id,
                	c.id company_id,
                	c.full_business_name company_name,
                	j.openings count_num,
                	GROUP_CONCAT( DISTINCT concat( t.id, "-", t.full_name )) contacts,
                	group_concat( DISTINCT concat( u.id, "-", u.first_name, " ", u.last_name )) assigned_user,
                	j.STATUS job_status,
                	j.start_date_format job_start_date,
                	j.currency job_currency,
                	jai.extended_info ->> '$.salaryRange.gte' AS minimum_pay_rate,
                	jai.extended_info ->> '$.salaryRange.lte' AS maximum_pay_rate,
                	if(j.sales_lead_id is null, null, if(ab.business_progress = 60, "Master Contract", "Trial Case")) job_cooperation_status
                FROM
                	company c
                	inner join job j on c.id = j.company_id
                	left join recruitment_process rp on rp.id = j.recruitment_process_id
                	LEFT JOIN job_company_contact_relation jccr ON jccr.job_id = j.id
                	LEFT JOIN company_sales_lead_client_contact cslcc ON cslcc.id = jccr.client_contact_id
                	LEFT JOIN talent t ON t.id = cslcc.talent_id
                    LEFT JOIN company_user_relation ul ON ul.company_id = c.id
                	LEFT JOIN user_job_relation ujr ON ujr.job_id = j.id AND ujr.status = 1
                	LEFT JOIN USER u ON u.id = ujr.user_id
                	LEFT JOIN job_additional_info jai ON jai.id = j.additional_info_id
                	LEFT JOIN talent_recruitment_process trp ON trp.job_id = j.id
                	inner join permission_user_team put on put.user_id = ul.user_id and put.is_primary = 1
                    inner join permission_team pt on pt.id = put.team_id
                	left join account_business ab on ab.id = j.sales_lead_id
                WHERE
                    j.tenant_id = :tenantId
                	{whereClause}
                	{groupBy}
                """;
        Map<String, String> map = new HashMap<>(16);
        Map<String, Object> param = new HashMap<>(16);
        param.put("tenantId", searchDto.getSearchTenantId());
        map.put("selectFields", getSelectFieldsForJob(searchDto.getGroupByFieldList()));
        map.put("companyField", getCompanyField());
        map.put("companyTable", getCompanyTable());
        map.put("whereClause", getWhereClauseForJobForCompany(searchDto, param).toString());
        map.put("groupBy", getGroupByField(searchDto.getGroupByFieldList()));
        List<RecruitingKpiJobDetailCommonCountVO> voList = getClassList(sql, map, param, RecruitingKpiJobDetailCommonCountVO.class);
        stopWatch.stop();
        voList.forEach(vo -> {
            if (searchDto.getPermissionRespDTO() != null && Objects.equals(searchDto.getPermissionRespDTO().getTeamIdForPrivateJob(), vo.getPteamId())) {
                vo.setPrivateJob(true);
            }
        });
        log.info("[apn @{}] searchJobDetailTwoKpi, times = {}ms", searchDto.getSearchUserId(), stopWatch.getTotalTimeMillis());
        return voList;
    }

    private List<RecruitingKpiCommonCountVO> searchJobKpi(RecruitingKpiReportSearchDto searchDto) {
        StopWatch stopWatch = new StopWatch("searchJobTask");
        stopWatch.start();
        String sql = """
                SELECT
                    {selectFields}
                    count(distinct c.id) company_num,
                    group_concat(distinct concat(j.id, "-", j.openings)) ids
                FROM
                    company c
                    INNER JOIN company_user_relation ul ON ul.company_id = c.id
                    inner join permission_user_team put on put.user_id = ul.user_id and put.is_primary = 1
                    inner join permission_team pt on pt.id = put.team_id
                    inner join user u on u.id = ul.user_id
                    left join job j on j.company_id = c.id
                    {joinTables}
                WHERE
                    j.tenant_id = :tenantId
                    {whereClause}
                    {groupByField}
                """;
        Map<String, Object> param = new HashMap<>(16);
        param.put("tenantId", searchDto.getSearchTenantId());
        Map<String, String> map = new HashMap<>(16);
        map.put("selectFields", getSelectFieldsForJobByCompany(searchDto.getGroupByFieldList()));
        map.put("joinTables", getJoinTablesForJobByCompany(searchDto));
        StringBuilder whereClauseForJobForCompany = getWhereClauseForJobForCompany(searchDto, param);
        this.appendUserActiveStatus(whereClauseForJobForCompany, searchDto.getUser(), param);
        map.put("whereClause", whereClauseForJobForCompany.toString());

        map.put("groupByField", getGroupByFieldForJobByCompany(searchDto.getGroupByFieldList()));
        List<RecruitingKpiCommonCountVO> voList = getClassList(sql, map, param, RecruitingKpiCommonCountVO.class);
        //处理 openings
        voList.forEach(vo -> {
            long sum = Stream.of(vo.getIds().split(","))
                    .mapToLong(id -> {
                        String[] parts = id.split("-");
                        return parts.length > 1 ? Long.parseLong(parts[1]) : 0;
                    }).sum();
            vo.setCountNum(sum);
        });
        stopWatch.stop();
        log.info("[apn @{}] search job count task, times = {}ms", searchDto.getSearchUserId(), stopWatch.getTotalTimeMillis());
        return voList;
    }

    private String getCompanyField() {
        return """
               cl.official_country country,
               c.created_date company_created_date,
               IF(GROUP_CONCAT(aba.id) is null,
               null,
               GROUP_CONCAT(distinct CONCAT( COALESCE(bfa.user_id, ''),"-",COALESCE(bfa.sales_lead_role, ''), "-",COALESCE(aba.NAME, ''),"-",COALESCE(aba.id, ''), "-",COALESCE(abstr.service_type_id, '')))
               ) business_json,
               """;
    }

    private String getCompanyTable() {
        return """
                left join company_location cl on cl.company_id = c.id
                left join business_flow_administrator bfa on bfa.company_id = c.id
                left join account_business aba on aba.id = bfa.account_business_id
                left join account_business_service_type_relation abstr on abstr.account_business_id = aba.id
                """;
    }

    protected String getGroupByFieldForJobByCompany(List<RecruitingKpiGroupByFieldType> groupByFieldList) {
        return "group by " + groupByFieldList.stream().map(groupByFieldType -> {
            switch (groupByFieldType) {
                case JOB -> { return " j.id ";}
                case COMPANY -> { return " c.id ";}
                case TEAM -> {return " put.team_id ";}
                case USER -> { return " put.user_id ";}
                case DAY, WEEK, MONTH, QUARTER, YEAR -> { return " group_by_date ";}
                default -> { return "";}
            }
        }).collect(Collectors.joining(" , "));
    }

    protected StringBuilder getWhereClauseForJobForCompany(RecruitingKpiReportSearchDto searchDto, Map<String, Object> param) {
        StringBuilder sb = new StringBuilder();
        //通用搜索
        appendDateSearchForJob(sb, searchDto, param);
        appendJobIdForJob(sb, searchDto, param);
        //search company by company 只有公司搜索
        appendCompanySearch(sb, searchDto, param);
        appendTeamLimitByCompany(sb, searchDto, param);
        //search permission team
        appendPermissionTeamSearch(sb);
        return sb;
    }

    private String getJoinTablesForJobByCompany(RecruitingKpiReportSearchDto searchDto) {
        StringBuilder sb = new StringBuilder();
        if (ObjectUtil.isNotEmpty(searchDto.getJob()) && CollUtil.isNotEmpty(searchDto.getJob().getTypeList())) {
            sb.append(" left join recruitment_process rp on rp.id = j.recruitment_process_id ");
        }
        getJoinTablesForJobColumn(searchDto, sb);
        return sb.toString();
    }

    private String getSelectFieldsForJobByCompany(List<RecruitingKpiGroupByFieldType> groupByFieldList) {
        return groupByFieldList.stream().map(groupByFieldType -> {
            switch (groupByFieldType) {
                case DAY -> { return " d.date group_by_date, ";}
                case WEEK -> { return " d.start_of_week group_by_date, ";}
                case MONTH -> { return "  DATE_FORMAT(d.start_of_month, '%Y-%m' ) group_by_date, ";}
                case QUARTER -> { return " d.quarter_of_year group_by_date, ";}
                case YEAR -> { return " d.year group_by_date, ";}
                case COMPANY -> { return " c.id company_id, c.full_business_name company_name, ";}
                case JOB -> { return " j.id job_id, j.title job_title, ";}
                case USER -> { return " ul.user_id user_id, concat(u.first_name, \" \", u.last_name) user_name, ";}
                case TEAM -> { return " put.team_id team_id, pt.name team_name,";}
                default -> { return "";}
            }
        }).collect(Collectors.joining(" "));
    }

    public List<RecruitingKpiCommonCountVO> searchTalentKpi(RecruitingKpiReportSearchDto searchDto, boolean jobDetailFlag) {
        StopWatch stopWatch = new StopWatch("searchTalentTask");
        stopWatch.start();
        String sql = """
                SELECT
                	{selectFields}
                	count( DISTINCT t.id ) count_num
                FROM
                	talent t
                	INNER JOIN talent_recruitment_process trp ON trp.talent_id = t.id
                	INNER JOIN job j ON j.id = trp.job_id
                	INNER JOIN company c ON c.id = j.company_id
                	INNER JOIN company_user_relation ul ON ul.company_id = c.id
                	inner join user u on u.id = ul.user_id
                	inner join permission_user_team put on put.user_id = ul.user_id and put.is_primary = 1
                	inner join permission_team pt on pt.id = put.team_id
                	{joinTables}
                WHERE
                	t.tenant_id = :tenantId
                	{whereClause}
                	{groupByField}
                """;
        Map<String, Object> param = new HashMap<>(16);
        param.put("tenantId", searchDto.getSearchTenantId());
        Map<String, String> map = new HashMap<>(16);
        map.put("selectFields", getSelectFieldsByCompany(searchDto, null));
        map.put("joinTables", getJoinTablesForTalentByCompany(searchDto));
        map.put("whereClause", jobDetailFlag? getWhereClauseForTalentWithJobDetailByCompany(searchDto, param): getWhereClauseForTalentByCompany(searchDto, param));
        map.put("groupByField", getGroupByField(searchDto.getGroupByFieldList()));
        List<RecruitingKpiCommonCountVO> voList = getClassList(sql, map, param, RecruitingKpiCommonCountVO.class);
        stopWatch.stop();
        log.info("[apn @{}] search talent count task, jobDetailFlag = {}, times = {}ms", searchDto.getSearchUserId(), jobDetailFlag, stopWatch.getTotalTimeMillis());
        return voList;
    }

    protected String getWhereClauseForTalentByCompany(RecruitingKpiReportSearchDto searchDto, Map<String, Object> param) {
        StringBuilder sb = new StringBuilder();
        //时间查询
        appendDateSearchForTalent(sb, searchDto, param);
        if (ObjectUtil.isNotEmpty(searchDto.getJob()) && CollUtil.isNotEmpty(searchDto.getJob().getTypeList())) {
            sb.append(" and rp.job_type in :jobType ");
            param.put("jobType", searchDto.getJob().getTypeList().stream().map(JobType::toDbValue).toList());
        }
        //company search
        appendCompanySearch(sb, searchDto, param);
        appendTeamLimitByCompany(sb, searchDto, param);
        this.appendUserActiveStatus(sb, searchDto.getUser(), param);
        //search permission team
        appendPermissionTeamSearch(sb);
        return sb.toString();
    }

    protected String getSelectFieldsThisWeekByCompany(RecruitingKpiReportSearchDto searchDto, ReportTableType reportTableType) {
        List<String> result = searchDto.getGroupByFieldList().stream().map(groupByFieldType -> {
            switch (groupByFieldType) {
                case COMPANY -> { return " c.id company_id, c.full_business_name company_name, ";}
                case JOB -> { return " j.id job_id, j.title job_title, ";}
                case USER -> { return " u.id user_id, concat(u.first_name, \" \", u.last_name) user_name, ";}
                case TEAM -> { return " put.team_id team_id, pt.name team_name,";}
                default -> { return "";}
            }
        }).toList();
        List<String> notNullResult = result.stream().filter(x->StringUtils.isNotBlank(x)).collect(Collectors.toList());
        return notNullResult.stream().collect(Collectors.joining(" "));
    }

    protected String getSelectFieldsByCompany(RecruitingKpiReportSearchDto searchDto, ReportTableType reportTableType) {
        return searchDto.getGroupByFieldList().stream().map(groupByFieldType -> {
            switch (groupByFieldType) {
                case COMPANY -> { return " c.id company_id, c.full_business_name company_name, ";}
                case JOB -> { return " j.id job_id, j.title job_title, ";}
                case DAY -> { return " d.date group_by_date, ";}
                case WEEK -> { return " d.start_of_week group_by_date, ";}
                case MONTH -> { return " DATE_FORMAT(d.start_of_month, '%Y-%m' ) group_by_date, ";}
                case QUARTER -> { return " d.quarter_of_year group_by_date, ";}
                case YEAR -> { return " d.year group_by_date, ";}
                case USER -> { return " u.id user_id, concat(u.first_name, \" \", u.last_name) user_name, ";}
                case TEAM -> { return " put.team_id team_id, pt.name team_name,";}
                default -> { return "";}
            }
        }).collect(Collectors.joining(" "))
                + (reportTableType == null || Objects.equals(ReportTableType.INTERVIEW, reportTableType) ?
                "" :
                " count(DISTINCT CASE WHEN trpn.id IS NOT NULL THEN node.id END) currentCountNum," +
                        "count(distinct  case when trp.ai_score is not null and trpn.id IS NOT NULL then trp.id else null end ) as currentAiRecommendNum, " +
                        "count(distinct  case when rf.id is not null and trpn.id IS NOT NULL then trp.id else null end ) as currentPrecisionAiRecommendNum, ");
    }

    private String getWhereClauseForTalentWithJobDetailByCompany(RecruitingKpiReportSearchDto searchDto, Map<String, Object> param) {
        StringBuilder sb = new StringBuilder();
        //携带 job的查询条件
        sb.append(getWhereClauseForJobForCompany(searchDto, param).toString());
        // Talent 查询条件, 只携带时间查询条件
        sb.append(getWhereClauseForTalentByCompany(searchDto, param));
        this.appendUserActiveStatus(sb, searchDto.getUser(), param);
        //search permission team
        appendPermissionTeamSearch(sb);
        return sb.toString();
    }

    private String getWhereClauseForApplicationWithJobDetailByCompany(RecruitingKpiReportSearchDto searchDto, Map<String, Object> param, ReportTableType reportTableType) {
        StringBuilder sb = new StringBuilder();
        //携带 job的查询条件
        sb.append(getWhereClauseForJobForCompany(searchDto, param).toString());
        // Talent 查询条件, 只携带时间查询条件
        sb.append(getWhereClauseForApplicationByCompany(searchDto, param, reportTableType));
        this.appendUserActiveStatus(sb, searchDto.getUser(), param);
        //search permission team
        appendPermissionTeamSearch(sb);
        return sb.toString();
    }

    private String getJoinTablesForTalentByCompany(RecruitingKpiReportSearchDto searchDto) {
        String timezone = searchDto.getTimezone();
        StringBuilder sb = new StringBuilder();
        if (shouldJoinDateDimension(searchDto)) {
            String dateField = getTimezoneField("t", timezone, "created_date");
            sb.append(" inner join date_dimension d on d.date = ").append(dateField).append(" ");
        }
        if (ObjectUtil.isNotEmpty(searchDto.getJob()) && CollUtil.isNotEmpty(searchDto.getJob().getTypeList())) {
            sb.append(" left join recruitment_process rp on rp.id = j.recruitment_process_id ");
        }
        return sb.toString();
    }

    protected String getJoinTablesForApplicationByCompany(RecruitingKpiReportSearchDto searchDto, ReportTableType reportTableType) {
        String timezone = searchDto.getTimezone();
        RecruitingKpiDateType dateType = searchDto.getDateType();
        StringBuilder joinClauses = new StringBuilder(getCurrentNodeType(reportTableType, dateType));
        if (ObjectUtil.isNotEmpty(searchDto.getJob()) && CollUtil.isNotEmpty(searchDto.getJob().getTypeList())) {
            joinClauses.append(" left join recruitment_process rp on rp.id = j.recruitment_process_id ");
        }
        // 添加日期维度连接条件，如果需要按日期分组
        if (shouldJoinDateDimension(searchDto)) {
            String dateField = determineDateField(dateType, reportTableType, timezone);
            joinClauses.append(" INNER JOIN date_dimension d ON d.date = ").append(dateField).append(" ");
        }
        return joinClauses.toString();
    }

    private String determineDateField(RecruitingKpiDateType dateType, ReportTableType reportTableType, String timezone) {
        return switch (dateType) {
            case ADD -> getTimezoneField("node", timezone, "created_date");
            case EVENT -> switch (reportTableType) {
                case SUBMIT_TO_CLIENT -> "node.submit_time_format";
                case INTERVIEW -> getTimezoneField("node", timezone, "from_time");
                case ON_BOARD -> "trpod.onboard_date";
                default -> getTimezoneField("node", timezone, "created_date");
            };
        };
    }

    private String getJoinTablesForJobNoteByCompany(RecruitingKpiReportSearchDto searchDto) {
        StringBuilder sb = new StringBuilder();
        if (ObjectUtil.isNotEmpty(searchDto.getJob()) && CollUtil.isNotEmpty(searchDto.getJob().getTypeList())) {
            sb.append(" left join recruitment_process rp on rp.id = j.recruitment_process_id ");
        }
        //job note 只受到 job company 的影响, 不受到 talent的 影响
        // 添加日期维度连接条件，如果需要按日期分组
        if (shouldJoinDateDimension(searchDto)) {
            String dateField = getTimezoneField( "jn", searchDto.getTimezone(), "created_date");
            sb.append(" INNER JOIN date_dimension d ON d.date = ").append(dateField).append(" ");
        }
        return sb.toString();
    }

    private String getWhereClauseForJobNoteByCompany(RecruitingKpiReportSearchDto searchDto, Map<String, Object> param) {
        StringBuilder sb = new StringBuilder();
        // Append date range condition to the query based on timezone and grouping flag
//        appendDateSearchForJobNote(searchDto, param, sb);
//        appendJobNoteUserIdListAndTeamIdList(searchDto, sb, param);
        //company search
        appendJobIdForJob(sb, searchDto, param);
        appendCompanySearch(sb, searchDto, param);
        appendTeamLimitByCompany(sb, searchDto, param);
        this.appendUserActiveStatus(sb, searchDto.getUser(), param);
        //search permission team
        appendPermissionTeamSearch(sb);
        return sb.toString();
    }

    private String getWhereClauseForJobNoteWithJobDetailByCompany(RecruitingKpiReportSearchDto searchDto, Map<String, Object> param) {
        //为 jobDetail 的时候控制查询条件, 在 kpi by company 的是,数据需要受到job的影响
        StringBuilder sb = new StringBuilder();
        // 这个是,如果包含了 job 的 job_user_relation
        sb.append(getWhereClauseForJobForCompany(searchDto, param).toString());
        sb.append(getWhereClauseForJobNoteByCompany(searchDto, param));
        this.appendUserActiveStatus(sb, searchDto.getUser(), param);
        //search permission team
        appendPermissionTeamSearch(sb);
        return sb.toString();
    }

    protected String getWhereClauseForApplicationByCompany(RecruitingKpiReportSearchDto searchDto, Map<String, Object> param, ReportTableType reportTableType) {
        StringBuilder sb = new StringBuilder();
        //application 通用搜索
        appendDateSearchForApplication(sb, searchDto, param, reportTableType);
        if (ObjectUtil.isNotEmpty(searchDto.getJob()) && CollUtil.isNotEmpty(searchDto.getJob().getTypeList())) {
            sb.append(" and rp.job_type in :jobType ");
            param.put("jobType", searchDto.getJob().getTypeList().stream().map(JobType::toDbValue).toList());
        }
        //company search
        appendCompanySearch(sb, searchDto, param);
        appendTeamLimitByCompany(sb, searchDto, param);
        this.appendUserActiveStatus(sb, searchDto.getUser(), param);
        //search permission team
        appendPermissionTeamSearch(sb);
        return sb.toString();
    }

    public List<KpiReportCompanyInfoVO> findCompanyInfoMapBy(List<Long> companyIdList, RecruitingKpiReportSearchDto searchDto) {
        String sql = """
                SELECT
                    CONCAT(c.id, "-", bfa.user_id, "-", bfa.sales_lead_role,"-",bfa.id) id,
                    c.id AS company_id,
                    c.active,
                    cl.official_country country,
                    c.created_date company_created_date,
                    c.request_date,
                    bfa.user_id,
                    u.username,
                    bfa.sales_lead_role,
                    bfa.country as role_country,
                    GROUP_CONCAT(DISTINCT CONCAT(COALESCE(ab.id, ""), '|||||', COALESCE(ab.name, ""), '|||||', COALESCE(abs.service_type_id, ""))) AS business_info,
                    group_concat(distinct cir.industry_id) industries, 
                    count( distinct ccn.id ) company_note_count
                FROM
                    company c
                    left join company_industry_relation cir on cir.company_id = c.id
                    inner join company_location cl on cl.company_id = c.id
                    INNER JOIN business_flow_administrator bfa ON bfa.company_id = c.id
                    INNER JOIN account_business ab ON ab.id = bfa.account_business_id
                    INNER JOIN account_business_service_type_relation abs ON abs.account_business_id = bfa.account_business_id
                    left join company_client_note ccn on ccn.company_id = c.id and ccn.deleted = 0
                    left join user u on u.id = bfa.user_id
                """;
        sql = sql + " where c.id in ?1 ";
        sql = sql + """
                    GROUP BY c.id, bfa.user_id, bfa.sales_lead_role,bfa.country;
                """;
       return searchData(sql, KpiReportCompanyInfoVO.class, Map.of(1, companyIdList));
    }

    public ConcurrentMap<Long, List<KpiReportCompanySubmitToClientTwoWeekVO>> findSubmitToClientWithWeek(RecruitingKpiReportSearchDto searchDTO) {
        StopWatch stopWatch = new StopWatch();
        stopWatch.start("[1] search company base info");
        RecruitingKpiReportSearchDto copySearchDto = ObjectUtil.cloneByStream(searchDTO);

        //获取当天的时间,在获取本周和上一周的时间范围
        LocalDate nowLocalDateForTimezone = Instant.now().atZone(ZoneId.of(copySearchDto.getTimezone())).toLocalDate();
        LocalDate thisWeekMonday = nowLocalDateForTimezone.with(TemporalAdjusters.previousOrSame(DayOfWeek.MONDAY));
        LocalDate lastWeekMonday = thisWeekMonday.minusWeeks(1);
        LocalDate thisWeekSunday = thisWeekMonday.plusDays(6);
        copySearchDto.setStartDate(lastWeekMonday.toString());
        copySearchDto.setEndDate(thisWeekSunday.toString());

        String sql = """
                SELECT
                    {selectFields}
                	c.active,
                    cl.official_country country,
                    c.created_date company_created_date,
                    c.request_date,
	                d.start_of_week group_by_date,
	                group_concat(distinct cir.industry_id) industries, 
                    count(distinct node.id) count_num,
                    count(DISTINCT CASE WHEN trpn.id IS NOT NULL THEN node.id END) currentCountNum,
                    count( distinct ccn.id )company_note_count,
                    count(distinct  case when trp.ai_score is not null then trp.id else null end ) as aiRecommendNum,
                    count(DISTINCT CASE WHEN trpn.id IS NOT NULL and trp.ai_score is not null THEN trp.id else null END) currentAiRecommendNum,
                    count(distinct  case when rf.id is not null then trp.id else null end ) as precisionAiRecommendNum,
                    count(DISTINCT CASE WHEN trpn.id IS NOT NULL and rf.id is not null THEN trp.id else null END) currentPrecisionAiRecommendNum
                FROM
                	talent_recruitment_process_submit_to_client node
                    inner join talent_recruitment_process trp on node.talent_recruitment_process_id = trp.id
                	inner join job j on j.id = trp.job_id
                	inner join company c on c.id = j.company_id
                	left join company_industry_relation cir on cir.company_id = c.id
                	LEFT JOIN company_user_relation ul ON ul.company_id = c.id
                	left join user u on u.id = ul.user_id
                    inner join company_location cl on cl.company_id = c.id
                    left join company_client_note ccn on ccn.company_id = c.id and ccn.deleted = 0
                    inner join permission_user_team put on put.user_id = u.id AND put.is_primary = 1
                    inner join permission_team pt on pt.id = put.team_id
                    {joinTable}
                    left join talent_recruitment_process_node trpn on trpn.talent_recruitment_process_id = trp.id and trpn.node_type = 20 and trpn.node_status = 1
                    left JOIN recruitment_process rp ON rp.id = j.recruitment_process_id
                    left join (select DISTINCT case when reason='UNLOCK_CANDIDATE' || reason='ADD_TO_TALENT' then ct.talent_id else rf.talent_id end as talent_id,rf.id,rf.job_id
                                from job_talent_recommend_feedback rf
                               left join credit_transaction ct on ct.profile_id = rf.talent_id
                               where  rf.reason in('ADD_TO_POSITION','ADD_TO_ASSOCIATION_JOB_FOLDER','ADD_TO_TALENT','UNLOCK_CANDIDATE')) rf on rf.job_id = trp.job_id and rf.talent_id = trp.talent_id 
                WHERE c.tenant_id = :tenantId
                	{dateWhere}
                	{whereClause}
                	{groupByField}
                """;
        StringBuilder joinClauses = new StringBuilder();
        boolean flag = RecruitingKpiDateType.ADD == copySearchDto.getDateType();
        // 添加日期维度连接条件，如果需要按日期分组
        String dateField = determineDateField(copySearchDto.getDateType(), ReportTableType.SUBMIT_TO_CLIENT, copySearchDto.getTimezone());
        joinClauses.append(" INNER JOIN date_dimension d ON d.date = ").append(dateField).append(" ");
        Map<String, String> map = new HashMap<>(16);
        map.put("dateWhere", " and node.created_date between :startDate and :endDate");
        Map<String, Object> param = new HashMap<>(16);
        /*if(Stream.of(RecruitingKpiGroupByFieldType.DAY, RecruitingKpiGroupByFieldType.WEEK, RecruitingKpiGroupByFieldType.MONTH, RecruitingKpiGroupByFieldType.QUARTER, RecruitingKpiGroupByFieldType.YEAR)
                    .anyMatch(copySearchDto.getGroupByFieldList()::contains)){
            copySearchDto.setGroupByFieldList(Arrays.asList(RecruitingKpiGroupByFieldType.COMPANY, RecruitingKpiGroupByFieldType.USER));
        }*/
        if (copySearchDto.isXxlJobFlag()) {
            copySearchDto.setGroupByFieldList(List.of(RecruitingKpiGroupByFieldType.COMPANY));
        }
        map.put("groupByField", getGroupByFieldThisWeek(copySearchDto.getGroupByFieldList()) + "c.active,c.created_date ,d.start_of_week");
        map.put("selectFields", getSelectFieldsThisWeekByCompany(copySearchDto, null));
        param.put("startDate", flag? copySearchDto.getStartDateUtc(): copySearchDto.getStartDate());
        param.put("endDate", flag? copySearchDto.getEndDateUtc(): copySearchDto.getEndDate());
        param.put("tenantId", copySearchDto.getSearchTenantId());
        map.put("joinTable", joinClauses.toString());
        map.put("whereClause", getWhereClauseByCompany(copySearchDto, param));


        List<RecruitingKpiApplicationCommonCountVO> voList = getClassList(sql, map, param, RecruitingKpiApplicationCommonCountVO.class);
        stopWatch.stop();
        stopWatch.start("[2] search business info");
        if (CollUtil.isEmpty(voList)) {
            stopWatch.stop();
            log.info("[apn @{}]null findSubmitToClientWithWeek task , times = {}ms \n {}", searchDTO.getSearchUserId(), stopWatch.getTotalTimeMillis(), stopWatch.prettyPrint());
            return new ConcurrentHashMap<>();
        }

//        List<Long> companyIdList = voList.stream().map(RecruitingKpiApplicationCommonCountVO::getCompanyId).collect(Collectors.toList());
//        Map<Long,List<KpiReportCompanySubmitToClientTwoWeekBusinessInfoVO>> businessInfoList = this.findBusinessByCompanyId(companyIdList,copySearchDto);
        stopWatch.stop();
        stopWatch.start("[3] assemble data task");

        List<KpiReportCompanySubmitToClientTwoWeekVO> list = new ArrayList<>();
        voList.forEach(vo -> {
            KpiReportCompanySubmitToClientTwoWeekVO kpiReportCompanySubmitToClientTwoWeekVO = new KpiReportCompanySubmitToClientTwoWeekVO();
            kpiReportCompanySubmitToClientTwoWeekVO.setCompanyId(vo.getCompanyId());
            kpiReportCompanySubmitToClientTwoWeekVO.setCompanyName(vo.getCompanyName());
            kpiReportCompanySubmitToClientTwoWeekVO.setRequestDate(vo.getRequestDate());
            kpiReportCompanySubmitToClientTwoWeekVO.setCompanyCreatedDate(vo.getCompanyCreatedDate());
//            if (businessInfoList.containsKey(vo.getCompanyId())) {
//                kpiReportCompanySubmitToClientTwoWeekVO.setBusinessInfo(businessInfoList.get(vo.getCompanyId()));
//            }
            kpiReportCompanySubmitToClientTwoWeekVO.setUsername(vo.getUserName());
            kpiReportCompanySubmitToClientTwoWeekVO.setUserId(vo.getUserId());
            kpiReportCompanySubmitToClientTwoWeekVO.setTeamId(vo.getTeamId());
            kpiReportCompanySubmitToClientTwoWeekVO.setTeamName(vo.getTeamName());
            kpiReportCompanySubmitToClientTwoWeekVO.setJobId(vo.getJobId());
            kpiReportCompanySubmitToClientTwoWeekVO.setJobTitle(vo.getJobTitle());
            kpiReportCompanySubmitToClientTwoWeekVO.setCompanyNoteCount(vo.getCompanyNoteCount());
            if(vo.getActive() != null){
                kpiReportCompanySubmitToClientTwoWeekVO.setActive(AccountCompanyStatus.fromDbValue(vo.getActive()));
            }
            kpiReportCompanySubmitToClientTwoWeekVO.setIndustries(vo.getIndustries());
            kpiReportCompanySubmitToClientTwoWeekVO.setCountry(vo.getCountry());
            if (lastWeekMonday.isEqual(LocalDate.parse(vo.getGroupByDate()))) {
                kpiReportCompanySubmitToClientTwoWeekVO.setLastWeek(vo.getGroupByDate());
                kpiReportCompanySubmitToClientTwoWeekVO.setLastWeekCount(vo.getCountNum());
                kpiReportCompanySubmitToClientTwoWeekVO.setLastCurrentCountNum(vo.getCurrentCountNum());
                kpiReportCompanySubmitToClientTwoWeekVO.setLastWeekCountAiRecommendNum(vo.getAiRecommendNum());
                kpiReportCompanySubmitToClientTwoWeekVO.setLastCurrentCountNumAiRecommendNum(vo.getCurrentAiRecommendNum());
                kpiReportCompanySubmitToClientTwoWeekVO.setLastWeekCountPrecisionAiRecommendNum(vo.getPrecisionAiRecommendNum());
                kpiReportCompanySubmitToClientTwoWeekVO.setLastCurrentCountNumPrecisionAiRecommendNum(vo.getCurrentPrecisionAiRecommendNum());

            }
            if (thisWeekMonday.isEqual(LocalDate.parse(vo.getGroupByDate()))) {
                kpiReportCompanySubmitToClientTwoWeekVO.setThisWeek(vo.getGroupByDate());
                kpiReportCompanySubmitToClientTwoWeekVO.setThisWeekCount(vo.getCountNum());
                kpiReportCompanySubmitToClientTwoWeekVO.setThisCurrentCountNum(vo.getCurrentCountNum());
                kpiReportCompanySubmitToClientTwoWeekVO.setThisWeekCountAiRecommendNum(vo.getAiRecommendNum());
                kpiReportCompanySubmitToClientTwoWeekVO.setThisCurrentCountNumAiRecommendNum(vo.getCurrentAiRecommendNum());
                kpiReportCompanySubmitToClientTwoWeekVO.setThisWeekCountPrecisionAiRecommendNum(vo.getPrecisionAiRecommendNum());
                kpiReportCompanySubmitToClientTwoWeekVO.setThisCurrentCountNumPrecisionAiRecommendNum(vo.getCurrentPrecisionAiRecommendNum());
            }
            list.add(kpiReportCompanySubmitToClientTwoWeekVO);
        });
        stopWatch.stop();
        log.info("[apn @{}]findSubmitToClientWithWeek task , times = {}ms  \n {}", searchDTO.getSearchUserId(), stopWatch.getTotalTimeMillis(), stopWatch.prettyPrint());
        return list.stream().collect(Collectors.groupingByConcurrent(KpiReportCompanySubmitToClientTwoWeekVO::getCompanyId));
    }

    private Map<Long,List<KpiReportCompanySubmitToClientTwoWeekBusinessInfoVO>> findBusinessByCompanyId(List<Long> companyIdList,RecruitingKpiReportSearchDto copySearchDto){

        String sql = """
                SELECT
                    c.id as company_id,
                    bfa.sales_lead_role,
                    bfa.country as role_country,
                    z.id user_id, concat(z.first_name, \" \", z.last_name) user_name,
                    GROUP_CONCAT(DISTINCT CONCAT(COALESCE(ab.id, ""), '|||||', COALESCE(ab.name, ""), '|||||', COALESCE(abs.service_type_id, ""))) AS business_info
                FROM
                	talent_recruitment_process_submit_to_client node
                    inner join talent_recruitment_process trp on node.talent_recruitment_process_id = trp.id
                	inner join job j on j.id = trp.job_id
                	inner join company c on c.id = j.company_id
                    inner join company_location cl on cl.company_id = c.id
                    INNER JOIN business_flow_administrator bfa ON bfa.company_id = c.id
                    INNER JOIN account_business ab ON ab.id = bfa.account_business_id
                    INNER JOIN account_business_service_type_relation abs ON abs.account_business_id = bfa.account_business_id
                    left join user z on z.id = bfa.user_id
                    {joinTable}
                WHERE c.tenant_id = :tenantId and c.id in :companyId 
                	{dateWhere}
                	group by c.id ,  z.id,bfa.sales_lead_role,bfa.country ORDER BY bfa.sales_lead_role
                """;
        StringBuilder joinClauses = new StringBuilder();
        boolean flag = RecruitingKpiDateType.ADD == copySearchDto.getDateType();
        // 添加日期维度连接条件，如果需要按日期分组
        String dateField = determineDateField(copySearchDto.getDateType(), ReportTableType.SUBMIT_TO_CLIENT, copySearchDto.getTimezone());
        joinClauses.append(" INNER JOIN date_dimension d ON d.date = ").append(dateField).append(" ");
        Map<String, String> map = new HashMap<>(16);
        map.put("dateWhere", " and " + dateField + """
                 between :startDate and :endDate
                """);
        map.put("joinTable", joinClauses.toString());

        Map<String, Object> param = new HashMap<>(16);
        param.put("startDate", flag? copySearchDto.getStartDateUtc(): copySearchDto.getStartDate());
        param.put("endDate", flag? copySearchDto.getEndDateUtc(): copySearchDto.getEndDate());
        param.put("tenantId", copySearchDto.getSearchTenantId());
        param.put("companyId", companyIdList);

//        if (ObjectUtil.isNotEmpty(copySearchDto.getUser()) && ObjectUtil.isNotEmpty(copySearchDto.getUser().getUserActiveStatus())){
//            param.put("activated", List.of(copySearchDto.getUser().getUserActiveStatus()));
//        }else {
//            param.put("activated", List.of(true, false));
//        }

        List<KpiReportCompanySubmitToClientTwoWeekBusinessInfoVO> voList = getClassList(sql, map, param, KpiReportCompanySubmitToClientTwoWeekBusinessInfoVO.class);
        if (CollUtil.isEmpty(voList)) {
            return new ConcurrentHashMap<>();
        }
        return voList.stream().collect(Collectors.groupingByConcurrent(KpiReportCompanySubmitToClientTwoWeekBusinessInfoVO::getCompanyId));
    }

    protected String getWhereClauseByCompany(RecruitingKpiReportSearchDto searchDto, Map<String, Object> param) {
        StringBuilder sb = new StringBuilder();
        appendCommonUserIdAndTeamId(searchDto,sb,param);
        //company search
        appendCompanySearchByTishWeek(sb, searchDto, param);
        //appendTeamLimitByCompany(sb,searchDto,param);
        appendTeamLimitByPrivateJob(sb,searchDto,param);
        if (ObjectUtil.isNotEmpty(searchDto.getJob()) && CollUtil.isNotEmpty(searchDto.getJob().getTypeList())) {
            sb.append(" and rp.job_type in :jobType ");
            param.put("jobType", searchDto.getJob().getTypeList().stream().map(JobType::toDbValue).toList());
        }
        this.appendUserActiveStatus(sb, searchDto.getUser(), param);
        //search permission team
        appendPermissionTeamSearch(sb);
        return sb.toString();
    }


    public List<KpiReportCompanyUpgradeToClientVO> findCompanyInfoMapByUserId(RecruitingKpiReportSearchDto searchDto, TeamDataPermissionRespDTO teamDTO) {
        String sql = """
                 SELECT
                   CONCAT(c.id, "-", bfa.user_id) id,
                   bfa.user_id ,
                   CONCAT(u.first_name, ' ', u.last_name) username,
                   pt.name team_name,
                   put.team_id,
                   '' group_by_date,
                   count(DISTINCT c.id ) as company_count
               FROM
                   company c
                   INNER JOIN business_flow_administrator bfa ON bfa.company_id = c.id
                   left join user u on u.id = bfa.user_id
                   inner join permission_user_team put on put.user_id = u.id and put.is_primary = 1
                   inner join permission_team pt on pt.id = put.team_id
               where c.active=30 and c.tenant_id = :tenantId
                     and date_format(CONVERT_TZ(c.request_date, 'UTC', :timezone),'%Y-%m-%d') BETWEEN :startDate AND :endDate
                """;
        if (shouldJoinDateDimension(searchDto)) {
            String data =  searchDto.getGroupByFieldList().stream().map(groupByFieldType -> {
                switch (groupByFieldType) {
                    case DAY -> { return " d.date group_by_date ";}
                    case WEEK -> { return " d.start_of_week group_by_date ";}
                    case MONTH -> { return "  DATE_FORMAT(d.start_of_month, '%Y-%m' ) group_by_date ";}
                    case QUARTER -> { return " d.quarter_of_year group_by_date ";}
                    case YEAR -> { return " d.year group_by_date ";}
                    default -> { return "";}
                }
            }).collect(Collectors.joining(" "));

            sql = " SELECT " +
                    "                   CONCAT(c.id, \"-\", bfa.user_id) id,\n" +
                    "                   bfa.user_id ,\n" +
                    "                   CONCAT(u.first_name, ' ', u.last_name) username,\n" +
                    "                   put.team_id,\n" +
                    "                   pt.name team_name,\n" +
                    "                   count(DISTINCT c.id ) as company_count," + data + " FROM\n" +
                    "                   company c\n" +
                    "                   INNER JOIN business_flow_administrator bfa ON bfa.company_id = c.id\n" +
                    "                   left join user u on u.id = bfa.user_id\n" +
                    "                   inner join permission_user_team put on put.user_id = u.id and put.is_primary = 1\n" +
                    "                   inner join permission_team pt on pt.id = put.team_id"+
                    " inner join date_dimension d on d.date =date_format(c.request_date,'%Y-%m-%d') "+
                    " where c.active=30 and c.tenant_id = :tenantId and date_format(CONVERT_TZ(c.request_date, 'UTC', :timezone),'%Y-%m-%d') BETWEEN :startDate AND :endDate ";
        }
        if (null != searchDto.getUserIdList() && !searchDto.getUserIdList().isEmpty() && null != searchDto.getTeamIdList() && !searchDto.getTeamIdList().isEmpty()) {
            sql = sql + " and (bfa.user_id in :userIdList or bfa.user_id in (select user_id from permission_user_team  where team_id in :teamIdList and is_primary = 1 ) ) ";
        } else {
            if (null != searchDto.getUserIdList() && !searchDto.getUserIdList().isEmpty()) {
                sql = sql + " and bfa.user_id in :userIdList ";
            }
            if (null != searchDto.getTeamIdList() && !searchDto.getTeamIdList().isEmpty()) {
                sql = sql + " and bfa.user_id in (select user_id from permission_user_team  where team_id in :teamIdList and is_primary = 1 ) ";
            }
        }

        if (BooleanUtil.isTrue(teamDTO.getSelf())) {
            //仅仅自己
            sql = sql + " and bfa.user_id = :puserId ";
        } else if (CollUtil.isNotEmpty(teamDTO.getNestedTeamIds())) {
            //团队
            //获取查询团队和权限团队的交集
            sql = sql + " and put.team_id in :teamIds " ;
        }

        boolean searchUserActiveStatus = ObjectUtil.isNotEmpty(searchDto.getUser()) && ObjectUtil.isNotEmpty(searchDto.getUser().getUserActiveStatus());

        if (searchUserActiveStatus){
            sql += " and u.activated =:activated ";
        }

        sql = sql + " group by bfa.user_id ";
        if (shouldJoinDateDimension(searchDto)) {
            String data = searchDto.getGroupByFieldList().stream().map(groupByFieldType -> {
                switch (groupByFieldType) {
                    case DAY, WEEK, MONTH, QUARTER, YEAR -> { return " group_by_date ";}
                    default -> { return "";}
                }
            }).collect(Collectors.joining(" "));
            sql = sql + "," + data;
        }


        Query query = entityManager.createNativeQuery(sql, KpiReportCompanyUpgradeToClientVO.class);
        if (null != searchDto.getUserIdList() && !searchDto.getUserIdList().isEmpty()) {
            query.setParameter("userIdList", searchDto.getUserIdList());
        }
        if (null != searchDto.getTeamIdList() && !searchDto.getTeamIdList().isEmpty()) {
            query.setParameter("teamIdList", searchDto.getTeamIdList());
        }
        if (BooleanUtil.isTrue(teamDTO.getSelf())) {
            //仅仅自己
            query.setParameter("puserId", searchDto.getSearchUserId());
        } else if (CollUtil.isNotEmpty(teamDTO.getNestedTeamIds())) {
            //团队
            //获取查询团队和权限团队的交集
            query.setParameter("teamIds", teamDTO.getNestedTeamIds());
        }

        query.setParameter("timezone",searchDto.getTimezone());
        query.setParameter("startDate", searchDto.getStartDate());
        query.setParameter("endDate", searchDto.getEndDate());
        query.setParameter("tenantId", SecurityUtils.getTenantId());

        if (searchUserActiveStatus){
            query.setParameter("activated", searchDto.getUser().getUserActiveStatus());
        }

        return query.getResultList();
    }

    public List<KpiReportCompanyUpgradeToClientVO> findCompanyInfoMapByTeamId(RecruitingKpiReportSearchDto searchDto,TeamDataPermissionRespDTO teamDTO) {
        String sql = """
                 SELECT
                   CONCAT(c.id, "-", put.team_id) id,
                   pt.name team_name,
                   put.team_id,
                   1 as user_id ,
                   '' as username,
                   '' as group_by_date,
                   count(DISTINCT c.id ) as company_count
               FROM
                   company c
                   INNER JOIN business_flow_administrator bfa ON bfa.company_id = c.id
                   inner join user u on u.id = bfa.user_id
                   INNER JOIN permission_user_team put ON put.user_id = u.id and put.is_primary = 1
                   inner join permission_team pt on pt.id = put.team_id
               where c.active=30 and c.tenant_id=:tenantId
                     and date_format(CONVERT_TZ(c.request_date, 'UTC', :timezone),'%Y-%m-%d') BETWEEN :startDate AND :endDate
                     
                """;
        if (shouldJoinDateDimension(searchDto)) {
            String data =  searchDto.getGroupByFieldList().stream().map(groupByFieldType -> {
                switch (groupByFieldType) {
                    case DAY -> { return " d.date group_by_date ";}
                    case WEEK -> { return " d.start_of_week group_by_date ";}
                    case MONTH -> { return "  DATE_FORMAT(d.start_of_month, '%Y-%m' ) group_by_date ";}
                    case QUARTER -> { return " d.quarter_of_year group_by_date ";}
                    case YEAR -> { return " d.year group_by_date ";}
                    default -> { return "";}
                }
            }).collect(Collectors.joining(" "));

            sql = " SELECT\n" +
                    "                   CONCAT(c.id, \"-\", put.team_id) id,\n" +
                    "                   pt.name team_name,\n" +
                    "                   put.team_id,\n" +
                    "                   1 as user_id ,\n" +
                    "                   '' as username,\n" +
                    "                   count(DISTINCT c.id ) as company_count," + data + " FROM\n" +
                    "                   company c\n" +
                    "                   INNER JOIN business_flow_administrator bfa ON bfa.company_id = c.id\n" +
                    "                   inner join user u on u.id = bfa.user_id\n" +
                    "                   inner join permission_user_team put on put.user_id = u.id and put.is_primary = 1\n" +
                    "                   inner join permission_team pt on pt.id = put.team_id"+
                    " inner join date_dimension d on d.date =date_format(c.request_date,'%Y-%m-%d') "+
                    " where c.active=30 and c.tenant_id = :tenantId and date_format(CONVERT_TZ(c.request_date, 'UTC', :timezone),'%Y-%m-%d') BETWEEN :startDate AND :endDate ";
        }
        if (null != searchDto.getUserIdList() && !searchDto.getUserIdList().isEmpty() && null != searchDto.getTeamIdList() && !searchDto.getTeamIdList().isEmpty()) {
            sql = sql + " and (bfa.user_id in :userIdList or bfa.user_id in (select user_id from permission_user_team  where team_id in :teamIdList and is_primary = 1 ) ) ";
        } else {
            if (null != searchDto.getUserIdList() && !searchDto.getUserIdList().isEmpty()) {
                sql = sql + " and bfa.user_id in :userIdList ";
            }
            if (null != searchDto.getTeamIdList() && !searchDto.getTeamIdList().isEmpty()) {
                sql = sql + " and bfa.user_id in (select user_id from permission_user_team  where team_id in :teamIdList and is_primary = 1 ) ";
            }
        }
        if (BooleanUtil.isTrue(teamDTO.getSelf())) {
            //仅仅自己
            sql = sql + " and put.user_id = :puserId  ";
        } else if (CollUtil.isNotEmpty(teamDTO.getNestedTeamIds())) {
            //团队
            //获取查询团队和权限团队的交集
            sql = sql + " and put.team_id in :teamIds " ;
        }

        boolean searchUserActiveStatus = ObjectUtil.isNotEmpty(searchDto.getUser()) && ObjectUtil.isNotEmpty(searchDto.getUser().getUserActiveStatus());

        if (searchUserActiveStatus){
            sql += " and u.activated =:activated ";
        }

        sql = sql + " group by put.team_id ";

        if (shouldJoinDateDimension(searchDto)) {
            String data = searchDto.getGroupByFieldList().stream().map(groupByFieldType -> {
                switch (groupByFieldType) {
                    case DAY, WEEK, MONTH, QUARTER, YEAR -> { return " group_by_date ";}
                    default -> { return "";}
                }
            }).collect(Collectors.joining(" "));
            sql = sql + "," + data;
        }

        Query query = entityManager.createNativeQuery(sql, KpiReportCompanyUpgradeToClientVO.class);
        if (null != searchDto.getUserIdList() && !searchDto.getUserIdList().isEmpty()) {
            query.setParameter("userIdList", searchDto.getUserIdList());
        }
        if (null != searchDto.getTeamIdList() && !searchDto.getTeamIdList().isEmpty()) {
            query.setParameter("teamIdList", searchDto.getTeamIdList());
        }
        if (BooleanUtil.isTrue(teamDTO.getSelf())) {
            //仅仅自己
            query.setParameter("puserId", searchDto.getSearchUserId());
        } else if (CollUtil.isNotEmpty(teamDTO.getNestedTeamIds())) {
            //团队
            //获取查询团队和权限团队的交集
            query.setParameter("teamIds", teamDTO.getNestedTeamIds());
        }
        query.setParameter("timezone",searchDto.getTimezone());
        query.setParameter("startDate", searchDto.getStartDate());
        query.setParameter("endDate", searchDto.getEndDate());
        query.setParameter("tenantId", SecurityUtils.getTenantId());
        if (searchUserActiveStatus){
            query.setParameter("activated", searchDto.getUser().getUserActiveStatus());
        }

        return query.getResultList();
    }

    public List<KpiReportCompanyUpgradeToClientVO> findCompanyInfoMapByDay(RecruitingKpiReportSearchDto searchDto,TeamDataPermissionRespDTO teamDTO) {

        String data =  searchDto.getGroupByFieldList().stream().map(groupByFieldType -> {
            switch (groupByFieldType) {
                case DAY -> { return " d.date group_by_date ";}
                case WEEK -> { return " d.start_of_week group_by_date ";}
                case MONTH -> { return "  DATE_FORMAT(d.start_of_month, '%Y-%m' ) group_by_date ";}
                case QUARTER -> { return " d.quarter_of_year group_by_date ";}
                case YEAR -> { return " d.year group_by_date ";}
                default -> { return "";}
            }
        }).collect(Collectors.joining(" "));
        StringBuilder dataSql = new StringBuilder();

        String sql= """
                   ,
                   CONCAT(c.id, "-", RAND(123456)) id,
                   count(DISTINCT c.id ) as company_count,
                   '' as team_id,
                   '' as user_id,
                   '' as team_name,
                   '' as username
               FROM
                   company c
                   INNER JOIN business_flow_administrator bfa ON bfa.company_id = c.id
                   inner join user u on u.id = bfa.user_id
                   INNER JOIN permission_user_team put ON put.user_id = u.id and put.is_primary = 1
                   inner join permission_team pt on pt.id = put.team_id
                   inner join date_dimension d on d.date =date_format(c.request_date,'%Y-%m-%d')
                   where c.active=30 and c.tenant_id = :tenantId and date_format(CONVERT_TZ(c.request_date, 'UTC', :timezone),'%Y-%m-%d') BETWEEN :startDate AND :endDate
                    """;

        dataSql.append(" select ").append(data).append(sql);

        if (null != searchDto.getUserIdList() && !searchDto.getUserIdList().isEmpty() && null != searchDto.getTeamIdList() && !searchDto.getTeamIdList().isEmpty()) {
            dataSql.append(" and (bfa.user_id in :userIdList or bfa.user_id in (select user_id from permission_user_team  where team_id in :teamIdList and is_primary = 1 ) ) ");
        } else {
            if (null != searchDto.getUserIdList() && !searchDto.getUserIdList().isEmpty()) {
                dataSql.append(" and bfa.user_id in :userIdList ");
            }
            if (null != searchDto.getTeamIdList() && !searchDto.getTeamIdList().isEmpty()) {
                dataSql.append(" and bfa.user_id in (select user_id from permission_user_team  where team_id in :teamIdList and is_primary = 1 ) ");
            }
        }
        if (BooleanUtil.isTrue(teamDTO.getSelf())) {
            //仅仅自己
            dataSql.append( " and put.user_id = :puserId  ");
        } else if (CollUtil.isNotEmpty(teamDTO.getNestedTeamIds())) {
            //团队
            //获取查询团队和权限团队的交集
            dataSql.append( " and put.team_id in :teamIds ");
        }

        boolean searchUserActiveStatus = ObjectUtil.isNotEmpty(searchDto.getUser()) && ObjectUtil.isNotEmpty(searchDto.getUser().getUserActiveStatus());

        if (searchUserActiveStatus){
            dataSql.append(" and u.activated =:activated ");
        }

        dataSql.append(" group by group_by_date ");

        Query query = entityManager.createNativeQuery(dataSql.toString(), KpiReportCompanyUpgradeToClientVO.class);
        if (null != searchDto.getUserIdList() && !searchDto.getUserIdList().isEmpty()) {
            query.setParameter("userIdList", searchDto.getUserIdList());
        }
        if (null != searchDto.getTeamIdList() && !searchDto.getTeamIdList().isEmpty()) {
            query.setParameter("teamIdList", searchDto.getTeamIdList());
        }
        if (BooleanUtil.isTrue(teamDTO.getSelf())) {
            //仅仅自己
            query.setParameter("puserId", searchDto.getSearchUserId());
        } else if (CollUtil.isNotEmpty(teamDTO.getNestedTeamIds())) {
            //团队
            //获取查询团队和权限团队的交集
            query.setParameter("teamIds", teamDTO.getNestedTeamIds());
        }
        query.setParameter("timezone",searchDto.getTimezone());
        query.setParameter("startDate", searchDto.getStartDate());
        query.setParameter("endDate", searchDto.getEndDate());
        query.setParameter("tenantId", SecurityUtils.getTenantId());
        if (searchUserActiveStatus){
            query.setParameter("activated", searchDto.getUser().getUserActiveStatus());
        }

        return query.getResultList();
    }


    public List<CrmUserIdAndTeamIdVO> findCrmUserIdByTeamId(List<Long> teamIdList, SearchUserDto user) {
        String sql = """
                select  CONCAT(u.id, "-", put.team_id) id,
                        u.id as user_id,
                        u.id as crm_user_id,
                        put.team_id,
                        pt.name team_name,
                        CONCAT(u.first_name, ' ', u.last_name) username
                        from permission_user_team put
                INNER JOIN user u on u.id = put.user_id
                 inner join permission_team pt on pt.id = put.team_id and pt.team_category_id IN (15,20)
                where team_id in :teamIdList and is_primary = 1 and u.activated in :activated
                """;

        Query query = entityManager.createNativeQuery(sql, CrmUserIdAndTeamIdVO.class);
        query.setParameter("teamIdList", teamIdList);
        query.setParameter("activated", (Objects.nonNull(user) && Objects.nonNull(user.getUserActiveStatus())) ? List.of(user.getUserActiveStatus()) : List.of(true, false));
        return query.getResultList();
    }

    public List<CrmUserIdAndTeamIdVO> findCrmUserIdByUserId(List<Long> userIdList, SearchUserDto user) {
        String sql = """
                select  u.id as  id,
                        u.id as user_id,
                        u.id as crm_user_id,
                        1 as team_id,
                        '' as team_name,
                        CONCAT(u.first_name, ' ', u.last_name) username
                       from user u 
                where u.id in :userIdList and u.activated in :activated
                """;

        Query query = entityManager.createNativeQuery(sql, CrmUserIdAndTeamIdVO.class);
        query.setParameter("userIdList", userIdList);
        query.setParameter("activated", (Objects.nonNull(user) && Objects.nonNull(user.getUserActiveStatus())) ? List.of(user.getUserActiveStatus()) : List.of(true, false));
        return query.getResultList();
    }

    public List<CrmUserIdAndTeamIdVO> findTeamIdAndUserIdByCrmUserId(List<Long> crmUserIdList, SearchUserDto user) {
        String sql = """
                select  CONCAT(u.id, "-", put.team_id) id,
                        u.id as user_id,
                        u.id as crm_user_id,
                        put.team_id,
                        pt.name team_name,
                        CONCAT(u.first_name, ' ', u.last_name) username
                        from permission_user_team put
                INNER JOIN user u on u.id = put.user_id
                 inner join permission_team pt on pt.id = put.team_id
                where u.id in :crmUserIdList and is_primary = 1  and u.activated in :activated
                """;

        Query query = entityManager.createNativeQuery(sql, CrmUserIdAndTeamIdVO.class);
        query.setParameter("crmUserIdList", crmUserIdList);
        query.setParameter("activated", (Objects.nonNull(user) && Objects.nonNull(user.getUserActiveStatus())) ? List.of(user.getUserActiveStatus()) : List.of(true, false));
        return query.getResultList();
    }

    public List<CrmUserIdAndTeamIdVO> findUserIdByCrmUserId(List<Long> crmUserIdList, SearchUserDto user) {
        String sql = """
                select  u.id id,
                        u.id as user_id,
                        u.id as crm_user_id,
                        1 as team_id,
                        '' as team_name,
                        CONCAT(u.first_name, ' ', u.last_name) username
                       from user as u
                where u.id in :crmUserIdList and u.activated in :activated
                """;

        Query query = entityManager.createNativeQuery(sql, CrmUserIdAndTeamIdVO.class);
        query.setParameter("crmUserIdList", crmUserIdList);
        query.setParameter("activated", (Objects.nonNull(user) && Objects.nonNull(user.getUserActiveStatus())) ? List.of(user.getUserActiveStatus()) : List.of(true, false));
        return query.getResultList();
    }

    public Pair<String,String> getDateInfo(RecruitingKpiReportSearchDto searchDto) {
        String timezone = getColumnSuffix(searchDto.getTimezone());
        boolean groupDateFlag = shouldJoinDateDimension(searchDto);
        if (StrUtil.isNotBlank(timezone)) {
            if (BooleanUtil.isTrue(groupDateFlag)) {
                return new Pair<>(searchDto.getStartDate(),searchDto.getEndDate());
            }
        }
        return new Pair<>(searchDto.getStartDateUtc(),searchDto.getEndDateUtc());
    }
}
