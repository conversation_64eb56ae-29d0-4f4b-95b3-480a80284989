package com.altomni.apn.common.domain.user;

import com.altomni.apn.common.enumeration.permission.DataScope;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import javax.persistence.*;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * A role (a security role) used by Spring Security.
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@Entity
@Table(name = "role")
public class Role implements Serializable {

    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Access(AccessType.PROPERTY)
    private Long id;

    @NotNull
    @Column(length = 50, name = "name")
    private String name;

    @NotNull
    @Column(name = "is_internal")
    private Boolean internal;

    @Column(name = "data_scope")
    private Integer dataScope;

    @Column(name = "client_contact_data_scope")
    private Integer clientContactDataScope= DataScope.PERMISSION_SELF.toDbValue();

    @Column(name = "report_data_scope")
    private Integer reportDataScope = DataScope.PERMISSION_SELF.toDbValue();

    @Column(name = "home_and_calendar_data_scope")
    private Integer homeAndCalendarDataScope = DataScope.PERMISSION_SELF.toDbValue();

    @Column(name = "candidate_pipeline_management_data_scope")
    private Integer candidatePipelineManagementDataScope = DataScope.PERMISSION_SELF.toDbValue();

    @Column(name = "china_invoicing_data_scope")
    private Integer chinaInvoicingDataScope = DataScope.PERMISSION_SELF.toDbValue();

    @Column(name = "confidential_talent_data_scope")
    private Integer confidentialTalentDataScope = DataScope.PERMISSION_SELF.toDbValue();

    @Column(name = "status")
    private Boolean status;

    @Column(name = "description")
    private String description;

    @Column(name = "tenant_id")
    private Long tenantId;
}
