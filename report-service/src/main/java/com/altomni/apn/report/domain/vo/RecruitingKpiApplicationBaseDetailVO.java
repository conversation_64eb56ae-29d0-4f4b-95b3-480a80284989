package com.altomni.apn.report.domain.vo;

import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.annotation.ExcelIgnore;
import com.altomni.apn.common.aop.confidential.AttachConfidentialTalent;
import com.altomni.apn.common.domain.enumeration.job.JobStatus;
import com.altomni.apn.common.domain.enumeration.job.JobType;
import com.altomni.apn.common.domain.enumeration.job.JobTypeConverter;
import com.altomni.apn.common.dto.application.dashboard.MyCandidateStatusFilter;
import com.altomni.apn.common.dto.application.dashboard.MyCandidateStatusFilterConverter;
import com.altomni.apn.common.dto.talent.ConfidentialInfoDto;
import com.altomni.apn.finance.service.vo.invoice.UserCountryVO;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import javax.persistence.Convert;
import javax.persistence.Id;
import javax.persistence.Transient;
import java.io.Serializable;
import java.time.Instant;
import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class RecruitingKpiApplicationBaseDetailVO implements Serializable, AttachConfidentialTalent {

    @Id
    private Long nodeId;

    private Long talentRecruitmentProcessId;

    private Long talentId;

    private String fullName;

    private Instant submitDate;

    private String jobTitle;

    private Long jobId;

    private String jobCode;

    private String companyName;

    private Long companyId;

    private JobType jobType;

    private JobStatus jobStatus;

    private MyCandidateStatusFilter nodeType;

    //后端去做 淘汰的兼容
    private MyCandidateStatusFilter workflowStatus;

    private String pipelineNote;

    private String jobLocation;

    private String am;

    private String coAm;

    @Transient
    private List<UserCountryVO> coAmList;

    private String recruiter;

    private String sourcer;

    private String ac;

    private String dm;

    private String owner;

    private String salesLeadOwner;

    private String bdOwner;

    private String lastModifiedBy;

    private Instant lastModifiedDate;

    private Long pteamId;

    @Transient
    private boolean isPrivateJob;

    private Boolean flexibleLocation;

    private Boolean stayedOver;

    /**
     * true: 已离职， false: 未离职
     */
    private Boolean resigned;

    /**
     * true: 是 converted to FTE 流程
     * false: 非 converted to FTE 流程
     */
    private Boolean convertedToFte;

    @Transient
    private ConfidentialInfoDto confidentialInfo;

    @Transient
    private Boolean confidentialTalentViewAble;

    public String getJobLocation() {
        return  flexibleLocation != null && flexibleLocation? (StrUtil.isBlank(jobLocation)? "remote": "(remote)" + jobLocation): jobLocation;
    }


    @Override
    public void setConfidentialTalentViewAble(Boolean confidentialTalentViewAble) {
        this.confidentialTalentViewAble = confidentialTalentViewAble;
    }

    @Override
    public void setConfidentialInfo(ConfidentialInfoDto confidentialInfo) {
        this.confidentialInfo = confidentialInfo;
    }


    @Override
    public void encrypt() {
        this.nodeId = null;
        this.talentRecruitmentProcessId = null;
        this.fullName = null;
        this.submitDate = null;
        this.jobTitle = null;
        this.jobId = null;
        this.jobCode = null;
        this.companyName = null;
        this.companyId = null;
        this.jobType = null;
        this.jobStatus = null;
        this.nodeType = null;
        this.workflowStatus = null;
        this.pipelineNote = null;
        this.jobLocation = null;
        this.am = null;
        this.coAm = null;
        this.recruiter = null;
        this.sourcer = null;
        this.ac = null;
        this.dm = null;
        this.owner = null;
        this.salesLeadOwner = null;
        this.bdOwner = null;
        this.lastModifiedBy = null;
        this.lastModifiedDate = null;
        this.pteamId = null;
        this.isPrivateJob = false;
        this.flexibleLocation = null;
        this.stayedOver = null;
        this.resigned = null;
        this.convertedToFte = null;
    }
}
