package com.altomni.apn.user.web.rest.vm.permission;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Set;

/**
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class PermissionUserTeamPermissionVM {

    private PermissionDetail jobPermission;

    private PermissionDetail clientContactPermission;

    private PermissionDetail reportPermission;

    private PermissionDetail homeAndCalendarPermission;

    private PermissionDetail candidatePipelineManagementPermission;

    private PermissionDetail chinaInvoicingModulePermission;

    private PermissionDetail confidentialTalentPermission;

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @JsonInclude(JsonInclude.Include.NON_NULL)
    public static class PermissionDetail{

        private Long userId;

        private Integer dataScope;

        private boolean modifiable = false;

        private Long primaryTeamId;

        private Set<Long> teamIds;
    }

}
