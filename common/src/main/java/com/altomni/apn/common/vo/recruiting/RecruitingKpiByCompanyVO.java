package com.altomni.apn.common.vo.recruiting;

import cn.hutool.core.collection.CollUtil;
import com.altomni.apn.common.domain.enumeration.job.JobStatus;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.Instant;
import java.util.ArrayList;
import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class RecruitingKpiByCompanyVO extends RecruitingKpiCommonVO {

    private String contacts;

    private String industries;

    private String assignedUser;

    private JobStatus jobStatus;

    private String jobStartDate;

    private String jobEndDate;

    private String contractDuration;

    private Long jobNoteNum;

    private String jobCurrency;

    private String minimumPayRate;

    private String maximumPayRate;

    private String jobCooperationStatus;

    private Long companyNum;

    private boolean isPrivateJob;

    private List<KpiReportCompanyInfoVO> amList;

    private List<KpiReportCompanyInfoVO> coAmList;

    private List<KpiReportCompanyInfoVO> salesLeadList;

    private List<KpiReportCompanyInfoVO> bdOwnerList;

    private Long bdReportProgressNoteCount;

    private Instant companyCreatedDate;

    private String country;

    private Long companyNoteCount;

    private String thisWeek;

    private String lastWeek;

    private Long thisWeekCount;

    private Long lastWeekCount;

    private Long lastWeekCurrentCountNum;

    private Long thisWeekCurrentCountNum;

    private Long thisWeekCountAIRecommend;

    private Long lastWeekCountAIRecommend;

    private Long lastWeekCurrentCountNumAIRecommend;

    private Long thisWeekCurrentCountNumAIRecommend;

    private Long thisWeekCountPrecisionAIRecommend;

    private Long lastWeekCountPrecisionAIRecommend;

    private Long lastWeekCurrentCountNumPrecisionAIRecommend;

    private Long thisWeekCurrentCountNumPrecisionAIRecommend;

    private String clientStatus;

    private Instant requestDate;
}
